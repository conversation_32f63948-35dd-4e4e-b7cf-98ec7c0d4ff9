#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
循环智能体实现：
- 支持设置最大循环次数
- 支持退出条件（通过事件的escalate标志或exit_loop工具）
- 循环执行子智能体直到满足退出条件或达到最大循环次数
- 兼容ADK的exit_loop工具机制
"""

from __future__ import annotations

from typing import AsyncGenerator, Optional, Any, List
from typing_extensions import override

try:
    from google.adk.agents.invocation_context import InvocationContext
    from google.adk.events.event import Event
    from google.adk.agents.base_agent import BaseAgent
    from google.adk.events.event_actions import EventActions
except ImportError:
    # 如果无法导入ADK，使用本地模拟类
    class InvocationContext:
        pass
    
    class EventActions:
        def __init__(self):
            self.escalate = None
    
    class Event:
        def __init__(self):
            self.actions = EventActions()
    
    class BaseAgent:
        def __init__(self, name: str = '', description: str = '', **kwargs):
            self.name = name
            self.description = description
            self.sub_agents: List[Any] = []
        
        async def run_async(self, ctx):
            yield Event()


class LoopAgent(BaseAgent):
    """
    循环智能体：循环执行其子智能体
    
    当子智能体生成带有escalate标志的事件或达到最大循环次数时，循环智能体将停止。
    """
    
    # Pydantic字段声明
    max_iterations: Optional[int] = None
    
    def __init__(self, name: str = '', description: str = '', max_iterations: Optional[int] = None, **kwargs):
        """
        初始化循环智能体
        
        Args:
            name: 智能体名称
            description: 智能体描述
            max_iterations: 最大循环次数，如果未设置则无限循环直到子智能体escalate
            **kwargs: 其他参数
        """
        super().__init__(name=name, description=description, max_iterations=max_iterations, **kwargs)
    
    @override
    async def _run_async_impl(self, ctx: InvocationContext) -> AsyncGenerator[Event, None]:
        """
        循环执行子智能体的异步实现
        
        支持两种退出机制：
        1. 最大循环次数限制
        2. 子智能体通过exit_loop工具或直接设置escalate标志退出
        
        Args:
            ctx: 调用上下文
            
        Yields:
            Event: 子智能体生成的事件
        """
        times_looped = 0
        while not self.max_iterations or times_looped < self.max_iterations:
            for sub_agent in self.sub_agents:
                async for event in sub_agent.run_async(ctx):
                    yield event
                    # 检查退出条件：如果事件包含escalate标志，则退出循环
                    # 这个标志可能通过exit_loop工具设置，也可能直接在事件中设置
                    if (hasattr(event, 'actions') and 
                        hasattr(event.actions, 'escalate') and 
                        event.actions.escalate):
                        return
            times_looped += 1
        return
    
    @override
    async def _run_live_impl(self, ctx: InvocationContext) -> AsyncGenerator[Event, None]:
        """
        循环执行子智能体的实时实现（暂不支持）
        
        Args:
            ctx: 调用上下文
            
        Yields:
            Event: 子智能体生成的事件
        """
        raise NotImplementedError('LoopAgent暂不支持实时模式')
        yield  # AsyncGenerator需要至少一个yield语句