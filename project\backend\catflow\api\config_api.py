from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.responses import <PERSON><PERSON><PERSON>esponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, Optional
import json
import os
from pathlib import Path

# 获取配置文件路径
CURRENT_DIR = Path(__file__).parent.parent
LLM_CONFIG_PATH = CURRENT_DIR / "llm_config.json"
TOOL_CONFIG_PATH = CURRENT_DIR / "tool_config.json"
AGENT_CONFIG_PATH = CURRENT_DIR / "agent_config.json"
STRUCT_CONFIG_PATH = CURRENT_DIR / "struct.json"

app = FastAPI(
    title="CatFlow配置管理API",
    description="管理LLM配置、工具配置、智能体配置和结构配置的API接口",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173",
        "http://127.0.0.1:5173",
        "http://localhost:5174",
        "http://127.0.0.1:5174"
    ],  # Vue开发服务器地址
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic模型定义
class LLMConfig(BaseModel):
    """LLM配置模型"""
    model: str
    api_base: str
    api_key: str
    api_key_env: str
    temperature: float
    input_cost_per_token: float
    output_cost_per_token: float

class LLMConfigUpdate(BaseModel):
    """LLM配置更新模型"""
    model: Optional[str] = None
    api_base: Optional[str] = None
    api_key: Optional[str] = None
    api_key_env: Optional[str] = None
    temperature: Optional[float] = None
    input_cost_per_token: Optional[float] = None
    output_cost_per_token: Optional[float] = None

class ToolConfig(BaseModel):
    """工具配置模型"""
    name: str
    descriptions: Dict[str, Any]
    type: str
    llm_name: str
    instruction: str
    temperature: Optional[float] = None
    json_schema: Optional[Dict[str, Any]] = None
    connection_type: Optional[str] = None
    connection_params: Optional[Dict[str, Any]] = None

class ToolConfigUpdate(BaseModel):
    """工具配置更新模型"""
    name: Optional[str] = None
    descriptions: Optional[Dict[str, Any]] = None
    type: Optional[str] = None
    llm_name: Optional[str] = None
    instruction: Optional[str] = None
    temperature: Optional[float] = None
    json_schema: Optional[Dict[str, Any]] = None
    connection_type: Optional[str] = None
    connection_params: Optional[Dict[str, Any]] = None

class GenerateContentConfig(BaseModel):
    """内容生成配置模型"""
    temperature: Optional[float] = None
    max_output_tokens: Optional[int] = None

class AgentConfig(BaseModel):
    """智能体配置模型"""
    name: str
    model: str
    instruction: str
    tools: Optional[list[str]] = []
    agent_type: str
    generate_content_config: Optional[GenerateContentConfig] = None

class AgentConfigUpdate(BaseModel):
    """智能体配置更新模型"""
    name: Optional[str] = None
    model: Optional[str] = None
    instruction: Optional[str] = None
    tools: Optional[list[str]] = None
    agent_type: Optional[str] = None
    generate_content_config: Optional[GenerateContentConfig] = None

class MainAgent(BaseModel):
    """主智能体配置模型"""
    name: str
    type: str
    model: Optional[str] = None
    max_iterations: Optional[int] = None
    instruction: str
    description: Optional[str] = None
    agent_refs: Optional[list[str]] = None  # 已弃用，保持向后兼容
    sub_structure_refs: Optional[list[str]] = None  # 新字段，用于引用子结构

class SubStructure(BaseModel):
    """子结构配置模型"""
    name: str
    type: str
    max_iterations: Optional[int] = None
    description: Optional[str] = None
    agent_refs: list[str]

class StructConfig(BaseModel):
    """结构配置模型"""
    name: str
    description: Optional[str] = None
    type: str
    global_instruction: Optional[str] = None
    generate_content_config: Optional[GenerateContentConfig] = None
    main_agent: MainAgent
    agent_refs: Optional[list[str]] = None  # 已弃用，保持向后兼容
    sub_structures: Optional[Dict[str, SubStructure]] = None

class StructConfigUpdate(BaseModel):
    """结构配置更新模型"""
    name: Optional[str] = None
    description: Optional[str] = None
    type: Optional[str] = None
    global_instruction: Optional[str] = None
    generate_content_config: Optional[GenerateContentConfig] = None
    main_agent: Optional[MainAgent] = None
    agent_refs: Optional[list[str]] = None  # 已弃用，保持向后兼容
    sub_structures: Optional[Dict[str, SubStructure]] = None

# 辅助函数
def read_json_file(file_path: Path) -> Dict[str, Any]:
    """读取JSON文件"""
    try:
        if not file_path.exists():
            raise HTTPException(status_code=404, detail=f"配置文件不存在: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except json.JSONDecodeError as e:
        raise HTTPException(status_code=400, detail=f"JSON格式错误: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"读取文件失败: {str(e)}")

def write_json_file(file_path: Path, data: Dict[str, Any]) -> None:
    """写入JSON文件"""
    try:
        # 确保目录存在
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"写入文件失败: {str(e)}")

# LLM配置相关API
@app.get("/llm-config", response_model=Dict[str, Any], summary="获取所有LLM配置")
async def get_llm_config():
    """获取所有LLM配置信息"""
    return read_json_file(LLM_CONFIG_PATH)

@app.get("/llm-config/{llm_name}", response_model=LLMConfig, summary="获取指定LLM配置")
async def get_llm_config_by_name(llm_name: str):
    """根据名称获取指定的LLM配置"""
    config_data = read_json_file(LLM_CONFIG_PATH)
    
    if "llms" not in config_data or llm_name not in config_data["llms"]:
        raise HTTPException(status_code=404, detail=f"LLM配置不存在: {llm_name}")
    
    return config_data["llms"][llm_name]

@app.post("/llm-config/{llm_name}", summary="创建或更新LLM配置")
async def create_or_update_llm_config(llm_name: str, config: LLMConfig):
    """创建或更新指定的LLM配置"""
    config_data = read_json_file(LLM_CONFIG_PATH)
    
    if "llms" not in config_data:
        config_data["llms"] = {}
    
    config_data["llms"][llm_name] = config.dict()
    write_json_file(LLM_CONFIG_PATH, config_data)
    
    return {"message": f"LLM配置 '{llm_name}' 已成功保存"}

@app.put("/llm-config/{llm_name}", summary="部分更新LLM配置")
async def update_llm_config(llm_name: str, config_update: LLMConfigUpdate):
    """部分更新指定的LLM配置"""
    config_data = read_json_file(LLM_CONFIG_PATH)
    
    if "llms" not in config_data or llm_name not in config_data["llms"]:
        raise HTTPException(status_code=404, detail=f"LLM配置不存在: {llm_name}")
    
    # 只更新提供的字段
    update_data = config_update.dict(exclude_unset=True)
    config_data["llms"][llm_name].update(update_data)
    
    write_json_file(LLM_CONFIG_PATH, config_data)
    
    return {"message": f"LLM配置 '{llm_name}' 已成功更新"}

@app.delete("/llm-config/{llm_name}", summary="删除LLM配置")
async def delete_llm_config(llm_name: str):
    """删除指定的LLM配置"""
    config_data = read_json_file(LLM_CONFIG_PATH)
    
    if "llms" not in config_data or llm_name not in config_data["llms"]:
        raise HTTPException(status_code=404, detail=f"LLM配置不存在: {llm_name}")
    
    del config_data["llms"][llm_name]
    write_json_file(LLM_CONFIG_PATH, config_data)
    
    return {"message": f"LLM配置 '{llm_name}' 已成功删除"}

# 工具配置相关API
@app.get("/tool-config", response_model=Dict[str, Any], summary="获取所有工具配置")
async def get_tool_config():
    """获取所有工具配置信息"""
    return read_json_file(TOOL_CONFIG_PATH)

@app.get("/tool-config/{tool_name}", response_model=ToolConfig, summary="获取指定工具配置")
async def get_tool_config_by_name(tool_name: str):
    """根据名称获取指定的工具配置"""
    config_data = read_json_file(TOOL_CONFIG_PATH)
    
    if "tools" not in config_data or tool_name not in config_data["tools"]:
        raise HTTPException(status_code=404, detail=f"工具配置不存在: {tool_name}")
    
    return config_data["tools"][tool_name]

@app.post("/tool-config/{tool_name}", summary="创建或更新工具配置")
async def create_or_update_tool_config(tool_name: str, config: ToolConfig):
    """创建或更新指定的工具配置"""
    config_data = read_json_file(TOOL_CONFIG_PATH)
    
    if "tools" not in config_data:
        config_data["tools"] = {}
    
    config_data["tools"][tool_name] = config.dict(exclude_unset=True)
    write_json_file(TOOL_CONFIG_PATH, config_data)
    
    return {"message": f"工具配置 '{tool_name}' 已成功保存"}

@app.put("/tool-config/{tool_name}", summary="部分更新工具配置")
async def update_tool_config(tool_name: str, config_update: ToolConfigUpdate):
    """部分更新指定的工具配置"""
    config_data = read_json_file(TOOL_CONFIG_PATH)
    
    if "tools" not in config_data or tool_name not in config_data["tools"]:
        raise HTTPException(status_code=404, detail=f"工具配置不存在: {tool_name}")
    
    # 只更新提供的字段
    update_data = config_update.dict(exclude_unset=True)
    config_data["tools"][tool_name].update(update_data)
    
    write_json_file(TOOL_CONFIG_PATH, config_data)
    
    return {"message": f"工具配置 '{tool_name}' 已成功更新"}

@app.delete("/tool-config/{tool_name}", summary="删除工具配置")
async def delete_tool_config(tool_name: str):
    """删除指定的工具配置"""
    config_data = read_json_file(TOOL_CONFIG_PATH)
    
    if "tools" not in config_data or tool_name not in config_data["tools"]:
        raise HTTPException(status_code=404, detail=f"工具配置不存在: {tool_name}")
    
    del config_data["tools"][tool_name]
    write_json_file(TOOL_CONFIG_PATH, config_data)
    
    return {"message": f"工具配置 '{tool_name}' 已成功删除"}

# 智能体配置相关API
@app.get("/agent-config", response_model=Dict[str, Any], summary="获取所有智能体配置")
async def get_agent_config():
    """获取所有智能体配置信息"""
    return read_json_file(AGENT_CONFIG_PATH)

@app.get("/agent-config/{agent_name}", response_model=AgentConfig, summary="获取指定智能体配置")
async def get_agent_config_by_name(agent_name: str):
    """根据名称获取指定的智能体配置"""
    config_data = read_json_file(AGENT_CONFIG_PATH)

    if "agents" not in config_data or agent_name not in config_data["agents"]:
        raise HTTPException(status_code=404, detail=f"智能体配置不存在: {agent_name}")

    return config_data["agents"][agent_name]

@app.post("/agent-config/{agent_name}", summary="创建或更新智能体配置")
async def create_or_update_agent_config(agent_name: str, config: AgentConfig):
    """创建或更新指定的智能体配置"""
    config_data = read_json_file(AGENT_CONFIG_PATH)

    if "agents" not in config_data:
        config_data["agents"] = {}

    config_data["agents"][agent_name] = config.dict(exclude_unset=True)
    write_json_file(AGENT_CONFIG_PATH, config_data)

    return {"message": f"智能体配置 '{agent_name}' 已成功保存"}

@app.put("/agent-config/{agent_name}", summary="部分更新智能体配置")
async def update_agent_config(agent_name: str, config_update: AgentConfigUpdate):
    """部分更新指定的智能体配置"""
    config_data = read_json_file(AGENT_CONFIG_PATH)

    if "agents" not in config_data or agent_name not in config_data["agents"]:
        raise HTTPException(status_code=404, detail=f"智能体配置不存在: {agent_name}")

    # 只更新提供的字段
    update_data = config_update.dict(exclude_unset=True)
    config_data["agents"][agent_name].update(update_data)

    write_json_file(AGENT_CONFIG_PATH, config_data)

    return {"message": f"智能体配置 '{agent_name}' 已成功更新"}

@app.delete("/agent-config/{agent_name}", summary="删除智能体配置")
async def delete_agent_config(agent_name: str):
    """删除指定的智能体配置"""
    config_data = read_json_file(AGENT_CONFIG_PATH)

    if "agents" not in config_data or agent_name not in config_data["agents"]:
        raise HTTPException(status_code=404, detail=f"智能体配置不存在: {agent_name}")

    del config_data["agents"][agent_name]
    write_json_file(AGENT_CONFIG_PATH, config_data)

    return {"message": f"智能体配置 '{agent_name}' 已成功删除"}

# 结构配置相关API
@app.get("/struct-config", response_model=Dict[str, Any], summary="获取所有结构配置")
async def get_struct_config():
    """获取所有结构配置信息"""
    return read_json_file(STRUCT_CONFIG_PATH)

@app.get("/struct-config/{struct_name}", response_model=StructConfig, summary="获取指定结构配置")
async def get_struct_config_by_name(struct_name: str):
    """根据名称获取指定的结构配置"""
    config_data = read_json_file(STRUCT_CONFIG_PATH)

    if "structures" not in config_data or struct_name not in config_data["structures"]:
        raise HTTPException(status_code=404, detail=f"结构配置不存在: {struct_name}")

    return config_data["structures"][struct_name]

@app.post("/struct-config/{struct_name}", summary="创建或更新结构配置")
async def create_or_update_struct_config(struct_name: str, config: StructConfig):
    """创建或更新指定的结构配置"""
    config_data = read_json_file(STRUCT_CONFIG_PATH)

    if "structures" not in config_data:
        config_data["structures"] = {}

    config_data["structures"][struct_name] = config.dict(exclude_unset=True)
    write_json_file(STRUCT_CONFIG_PATH, config_data)

    return {"message": f"结构配置 '{struct_name}' 已成功保存"}

@app.put("/struct-config/{struct_name}", summary="部分更新结构配置")
async def update_struct_config(struct_name: str, config_update: StructConfigUpdate):
    """部分更新指定的结构配置"""
    config_data = read_json_file(STRUCT_CONFIG_PATH)

    if "structures" not in config_data or struct_name not in config_data["structures"]:
        raise HTTPException(status_code=404, detail=f"结构配置不存在: {struct_name}")

    # 只更新提供的字段
    update_data = config_update.dict(exclude_unset=True)
    config_data["structures"][struct_name].update(update_data)

    write_json_file(STRUCT_CONFIG_PATH, config_data)

    return {"message": f"结构配置 '{struct_name}' 已成功更新"}

@app.delete("/struct-config/{struct_name}", summary="删除结构配置")
async def delete_struct_config(struct_name: str):
    """删除指定的结构配置"""
    config_data = read_json_file(STRUCT_CONFIG_PATH)

    if "structures" not in config_data or struct_name not in config_data["structures"]:
        raise HTTPException(status_code=404, detail=f"结构配置不存在: {struct_name}")

    del config_data["structures"][struct_name]
    write_json_file(STRUCT_CONFIG_PATH, config_data)

    return {"message": f"结构配置 '{struct_name}' 已成功删除"}

# 健康检查
@app.get("/health", summary="健康检查")
async def health_check():
    """API健康检查"""
    return {"status": "healthy", "message": "CatFlow配置管理API运行正常"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)