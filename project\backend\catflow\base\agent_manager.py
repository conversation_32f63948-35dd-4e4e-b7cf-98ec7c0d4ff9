#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Agent管理器：
- 从agent_config.json配置文件加载智能体配置
- 支持创建单个LlmAgent实例
- 集成LLMManager和ToolManager
"""

import json
import os
from typing import Dict, Any, Optional, List
from .llm_manager import LLMManager
from .tool_manager import ToolManager
from google.adk.agents.llm_agent import LlmAgent
from google.genai import types

class AgentManager:
    """
    智能体管理器：负责从配置文件加载和创建LlmAgent实例
    """
    
    def __init__(self, config_path: Optional[str] = None, llm_manager: Optional[LLMManager] = None, tool_manager: Optional[ToolManager] = None):
        """
        初始化智能体管理器
        :param config_path: 智能体配置文件路径，如果为None则使用默认路径
        :param llm_manager: LLM管理器实例，如果为None则创建新实例
        :param tool_manager: 工具管理器实例，如果为None则创建新实例
        """
        if config_path is None:
            config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'agent_config.json')
        
        self.config_path = config_path
        self.agent_configs = self._load_config(config_path)
        
        # 初始化LLM管理器和工具管理器
        self.llm_manager = llm_manager if llm_manager else LLMManager()
        self.tool_manager = tool_manager if tool_manager else ToolManager(self.llm_manager)
        
        # 缓存已创建的智能体
        self._agent_cache: Dict[str, LlmAgent] = {}
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """
        加载智能体配置文件
        :param config_path: 配置文件路径
        :return: 智能体配置字典
        """
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
        except FileNotFoundError:
            raise ValueError(f"找不到智能体配置文件：{config_path}")
        except json.JSONDecodeError:
            raise ValueError(f"智能体配置文件格式错误：不是有效的JSON格式")
        
        # 校验配置文件格式
        if 'agents' not in config or not isinstance(config['agents'], dict):
            raise ValueError("配置文件格式错误：缺少'agents'字段或类型不为dict")
        
        # 校验每个智能体配置
        for agent_name, agent_config in config['agents'].items():
            if not isinstance(agent_config, dict):
                raise ValueError(f"智能体配置错误：'{agent_name}' 配置应为dict类型")
            
            # 检查必需字段
            required_fields = ['name', 'model', 'instruction', 'agent_type']
            for field in required_fields:
                if field not in agent_config:
                    raise ValueError(f"智能体配置错误：'{agent_name}' 缺少必需字段 '{field}'")
            
            # 检查agent_type是否为LlmAgent
            if agent_config['agent_type'] != 'LlmAgent':
                raise ValueError(f"智能体配置错误：'{agent_name}' 的agent_type必须为'LlmAgent'")
        
        print(f"成功加载智能体配置文件：{config_path}")
        print(f"可用的智能体：{', '.join(config['agents'].keys())}")
        return config
    
    def list_agents(self) -> List[str]:
        """
        获取所有可用的智能体名称列表
        :return: 智能体名称列表
        """
        return list(self.agent_configs['agents'].keys())
    
    def get_agent_config(self, agent_name: str) -> Dict[str, Any]:
        """
        获取指定智能体的配置
        :param agent_name: 智能体名称
        :return: 智能体配置字典
        """
        if agent_name not in self.agent_configs['agents']:
            raise ValueError(f"智能体 '{agent_name}' 不存在")
        return self.agent_configs['agents'][agent_name]
    
    async def create_agent(self, agent_name: str, use_cache: bool = True) -> LlmAgent:
        """
        创建指定的智能体实例
        :param agent_name: 智能体名称
        :param use_cache: 是否使用缓存，如果为True且智能体已创建过，则返回缓存的实例
        :return: LlmAgent实例
        """
        # 检查缓存
        if use_cache and agent_name in self._agent_cache:
            return self._agent_cache[agent_name]
        
        # 获取智能体配置
        agent_config = self.get_agent_config(agent_name)
        
        # 获取模型
        model_name = agent_config['model']
        model = self.llm_manager.get_model(model_name)
        
        # 获取工具
        tools = await self._get_tools_for_agent(agent_config)
        
        # 处理 generate_content_config
        generate_content_config = None
        if 'generate_content_config' in agent_config:
            from google.genai import types
            config_data = agent_config['generate_content_config']
            generate_content_config = types.GenerateContentConfig(**config_data)
        
        # 创建智能体
        agent = LlmAgent(
            name=agent_config['name'],
            model=model,
            instruction=agent_config['instruction'],
            tools=tools,
            generate_content_config=generate_content_config  # 添加这一行
        )
        
        # 缓存智能体
        if use_cache:
            self._agent_cache[agent_name] = agent
        
        # 打印这个智能体信息，包括名称、模型、工具列表
        print(f"成功创建智能体：{agent_name}，模型：{model_name}，工具列表：{[tool.name for tool in tools]}")
        
        return agent
    
    async def _get_tools_for_agent(self, agent_config: Dict[str, Any]) -> List[Any]:
        """
        根据智能体配置获取工具列表
        :param agent_config: 智能体配置
        :return: 工具列表
        """
        # 如果智能体配置中没有tools字段，返回所有可用工具
        if 'tools' not in agent_config:
            all_tools = await self.tool_manager.get_tools()
            return all_tools
        
        # 如果tools字段为空列表，返回空列表
        if not agent_config['tools']:
            return []
        
        # 如果智能体配置中指定了工具列表，则过滤工具
        all_tools = await self.tool_manager.get_tools()
        specified_tool_names = agent_config['tools']
        filtered_tools = []
        
        for tool in all_tools:
            if tool.name in specified_tool_names:
                filtered_tools.append(tool)
        
        return filtered_tools
    
    def clear_cache(self) -> None:
        """
        清空智能体缓存
        """
        self._agent_cache.clear()
        print("智能体缓存已清空")