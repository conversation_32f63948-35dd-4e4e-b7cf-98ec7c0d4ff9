import * as Blockly from 'blockly'
import type { StructureConfig, SubStructure, MainAgent, BlockData } from '../types/structure'

/**
 * Blockly 工具类
 * 处理 Blockly 工作区和结构数据之间的转换
 */
export class BlocklyUtils {
  /**
   * 将结构配置转换为 Blockly 工作区
   * @param workspace Blockly 工作区
   * @param structureConfig 结构配置
   */
  static loadStructureToWorkspace(workspace: Blockly.WorkspaceSvg, structureConfig: StructureConfig) {
    // 清空工作区
    workspace.clear()
    
    // 创建结构块
    const structureBlock = workspace.newBlock('structure')
    structureBlock.setFieldValue(structureConfig.name, 'NAME')
    structureBlock.initSvg()
    structureBlock.render()
    
    // 设置结构块位置
    structureBlock.moveBy(50, 50)
    
    // 创建主智能体块
    if (structureConfig.main_agent) {
      const mainAgentBlock = this.createMainAgentBlock(workspace, structureConfig.main_agent)
      
      // 连接主智能体到结构块
      const connection = structureBlock.getInput('MAIN_AGENT')?.connection
      if (connection && mainAgentBlock.previousConnection) {
        connection.connect(mainAgentBlock.previousConnection)
      }
      
      // 创建子结构块
      if (structureConfig.main_agent.sub_structures) {
        this.createSubStructureBlocks(workspace, mainAgentBlock, structureConfig.main_agent.sub_structures)
      }
    }
    
    // 居中显示
    workspace.scrollCenter()
  }
  
  /**
   * 创建主智能体块
   * @param workspace Blockly 工作区
   * @param mainAgent 主智能体配置
   */
  private static createMainAgentBlock(workspace: Blockly.WorkspaceSvg, mainAgent: MainAgent): Blockly.Block {
    const block = workspace.newBlock('main_agent')
    block.setFieldValue(mainAgent.name, 'NAME')
    block.initSvg()
    block.render()
    
    return block
  }
  
  /**
   * 创建子结构块
   * @param workspace Blockly 工作区
   * @param parentBlock 父块
   * @param subStructures 子结构列表
   */
  private static createSubStructureBlocks(
    workspace: Blockly.WorkspaceSvg, 
    parentBlock: Blockly.Block, 
    subStructures: SubStructure[]
  ) {
    let previousBlock: Blockly.Block | null = null
    
    for (const subStructure of subStructures) {
      const subBlock = workspace.newBlock('sub_structure')
      subBlock.setFieldValue(subStructure.name, 'NAME')
      subBlock.initSvg()
      subBlock.render()
      
      // 连接到父块或前一个子结构块
      if (!previousBlock) {
        // 连接到父块的子结构输入
        const connection = parentBlock.getInput('SUB_STRUCTURES')?.connection
        if (connection && subBlock.previousConnection) {
          connection.connect(subBlock.previousConnection)
        }
      } else {
        // 连接到前一个子结构块
        if (previousBlock.nextConnection && subBlock.previousConnection) {
          previousBlock.nextConnection.connect(subBlock.previousConnection)
        }
      }
      
      // 创建智能体引用块
      if (subStructure.agent_refs) {
        this.createAgentRefBlocks(workspace, subBlock, subStructure.agent_refs)
      }
      
      // 递归创建嵌套子结构
      if (subStructure.sub_structures) {
        this.createSubStructureBlocks(workspace, subBlock, subStructure.sub_structures)
      }
      
      previousBlock = subBlock
    }
  }
  
  /**
   * 创建智能体引用块
   * @param workspace Blockly 工作区
   * @param parentBlock 父块
   * @param agentRefs 智能体引用列表
   */
  private static createAgentRefBlocks(
    workspace: Blockly.WorkspaceSvg, 
    parentBlock: Blockly.Block, 
    agentRefs: string[]
  ) {
    let previousBlock: Blockly.Block | null = null
    
    for (const agentRef of agentRefs) {
      const refBlock = workspace.newBlock('agent_ref')
      refBlock.setFieldValue(agentRef, 'AGENT_NAME')
      refBlock.initSvg()
      refBlock.render()
      
      // 连接到父块或前一个引用块
      if (!previousBlock) {
        // 连接到父块的嵌套内容输入
        const connection = parentBlock.getInput('NESTED_STRUCTURES')?.connection
        if (connection && refBlock.previousConnection) {
          connection.connect(refBlock.previousConnection)
        }
      } else {
        // 连接到前一个引用块
        if (previousBlock.nextConnection && refBlock.previousConnection) {
          previousBlock.nextConnection.connect(refBlock.previousConnection)
        }
      }
      
      previousBlock = refBlock
    }
  }
  
  /**
   * 将 Blockly 工作区转换为结构配置
   * @param workspace Blockly 工作区
   */
  static workspaceToStructureConfig(workspace: Blockly.WorkspaceSvg): StructureConfig | null {
    const topBlocks = workspace.getTopBlocks()
    const structureBlock = topBlocks.find(block => block.type === 'structure')
    
    if (!structureBlock) {
      return null
    }
    
    const structureConfig: StructureConfig = {
      name: structureBlock.getFieldValue('NAME') || '未命名结构',
      description: structureBlock.getFieldValue('DESCRIPTION') || '',
      type: structureBlock.getFieldValue('TYPE') || 'coordinator',
      global_instruction: structureBlock.getFieldValue('GLOBAL_INSTRUCTION') || '',
      main_agent: {
        name: 'main_agent',
        type: 'sequential_agent',
        model: 'qwen-max',
        instruction: '',
        description: ''
      }
    }

    // 处理生成配置
    const temperature = parseFloat(structureBlock.getFieldValue('TEMPERATURE'))
    const maxTokens = parseInt(structureBlock.getFieldValue('MAX_TOKENS'))
    if (!isNaN(temperature) || !isNaN(maxTokens)) {
      structureConfig.generate_content_config = {}
      if (!isNaN(temperature)) {
        structureConfig.generate_content_config.temperature = temperature
      }
      if (!isNaN(maxTokens)) {
        structureConfig.generate_content_config.max_output_tokens = maxTokens
      }
    }

    // 处理主智能体
    const mainAgentInput = structureBlock.getInput('MAIN_AGENT')
    if (mainAgentInput?.connection?.targetBlock()) {
      const mainAgentBlock = mainAgentInput.connection.targetBlock()
      if (mainAgentBlock) {
        structureConfig.main_agent = this.blockToMainAgent(mainAgentBlock)

        // 收集所有子结构定义
        const allSubStructures = this.collectAllSubStructures(mainAgentBlock)
        if (Object.keys(allSubStructures).length > 0) {
          structureConfig.sub_structures = allSubStructures
        }
      }
    }

    return structureConfig
  }
  
  /**
   * 将主智能体块转换为主智能体配置
   * @param block 主智能体块
   */
  private static blockToMainAgent(block: Blockly.Block): MainAgent {
    const mainAgent: MainAgent = {
      name: block.getFieldValue('NAME') || 'main_agent',
      type: block.getFieldValue('AGENT_TYPE') || 'sequential_agent',
      model: block.getFieldValue('MODEL') || 'qwen-max',
      instruction: block.getFieldValue('INSTRUCTION') || '',
      description: block.getFieldValue('DESCRIPTION') || ''
    }

    // 处理最大迭代次数（仅对循环智能体）
    if (mainAgent.type === 'loop_agent') {
      const maxIterationsInput = block.getInput('MAX_ITERATIONS')
      if (maxIterationsInput?.connection?.targetBlock()) {
        const numberBlock = maxIterationsInput.connection.targetBlock()
        mainAgent.max_iterations = parseInt(numberBlock.getFieldValue('VALUE')) || 1
      }
    }

    // 处理子结构引用
    const subStructuresInput = block.getInput('SUB_STRUCTURES')
    if (subStructuresInput?.connection?.targetBlock()) {
      const subStructures = this.blocksToSubStructures(subStructuresInput.connection.targetBlock())
      mainAgent.sub_structure_refs = subStructures.map(sub => sub.name)
    }

    return mainAgent
  }
  
  /**
   * 收集所有子结构定义
   * @param mainAgentBlock 主智能体块
   */
  private static collectAllSubStructures(mainAgentBlock: Blockly.Block): Record<string, SubStructure> {
    const allSubStructures: Record<string, SubStructure> = {}

    const subStructuresInput = mainAgentBlock.getInput('SUB_STRUCTURES')
    if (subStructuresInput?.connection?.targetBlock()) {
      this.collectSubStructuresRecursive(subStructuresInput.connection.targetBlock(), allSubStructures)
    }

    return allSubStructures
  }

  /**
   * 递归收集子结构定义
   * @param block 当前块
   * @param collection 收集器
   */
  private static collectSubStructuresRecursive(block: Blockly.Block, collection: Record<string, SubStructure>) {
    let currentBlock: Blockly.Block | null = block

    while (currentBlock) {
      if (currentBlock.type === 'sub_structure') {
        const subStructure: SubStructure = {
          name: currentBlock.getFieldValue('NAME') || '未命名子结构',
          type: currentBlock.getFieldValue('SUB_TYPE') || 'sequential',
          description: currentBlock.getFieldValue('DESCRIPTION') || '',
          agent_refs: []
        }

        // 处理最大迭代次数（仅对循环类型）
        if (subStructure.type === 'loop') {
          const maxIterationsInput = currentBlock.getInput('MAX_ITERATIONS')
          if (maxIterationsInput?.connection?.targetBlock()) {
            const numberBlock = maxIterationsInput.connection.targetBlock()
            subStructure.max_iterations = parseInt(numberBlock.getFieldValue('VALUE')) || 1
          }
        }

        // 处理内容
        const contentInput = currentBlock.getInput('CONTENT')
        if (contentInput?.connection?.targetBlock()) {
          const contentBlock = contentInput.connection.targetBlock()

          if (contentBlock.type === 'agent_ref') {
            subStructure.agent_refs = this.blocksToAgentRefs(contentBlock)
          } else if (contentBlock.type === 'sub_structure') {
            // 递归处理嵌套子结构
            this.collectSubStructuresRecursive(contentBlock, collection)
          }
        }

        collection[subStructure.name] = subStructure
      }

      currentBlock = currentBlock.getNextBlock()
    }
  }

  /**
   * 将子结构块链转换为子结构配置列表
   * @param firstBlock 第一个子结构块
   */
  private static blocksToSubStructures(firstBlock: Blockly.Block): SubStructure[] {
    const subStructures: SubStructure[] = []
    let currentBlock: Blockly.Block | null = firstBlock
    
    while (currentBlock) {
      if (currentBlock.type === 'sub_structure') {
        const subStructure: SubStructure = {
          name: currentBlock.getFieldValue('NAME') || '未命名子结构',
          type: 'sequential',
          description: ''
        }
        
        // 处理嵌套内容
        const contentInput = currentBlock.getInput('CONTENT')
        if (contentInput?.connection?.targetBlock()) {
          const contentBlock = contentInput.connection.targetBlock()

          // 检查是否是智能体引用
          if (contentBlock.type === 'agent_ref') {
            subStructure.agent_refs = this.blocksToAgentRefs(contentBlock)
          } else if (contentBlock.type === 'sub_structure') {
            // 嵌套子结构 - 需要创建子结构字典
            const nestedSubStructures = this.blocksToSubStructures(contentBlock)
            subStructure.sub_structures = {}
            nestedSubStructures.forEach((nested, index) => {
              subStructure.sub_structures![`sub_${index}`] = nested
            })
          }
        }
        
        subStructures.push(subStructure)
      }
      
      currentBlock = currentBlock.getNextBlock()
    }
    
    return subStructures
  }
  
  /**
   * 将智能体引用块链转换为智能体引用列表
   * @param firstBlock 第一个智能体引用块
   */
  private static blocksToAgentRefs(firstBlock: Blockly.Block): string[] {
    const agentRefs: string[] = []
    let currentBlock: Blockly.Block | null = firstBlock
    
    while (currentBlock) {
      if (currentBlock.type === 'agent_ref') {
        const agentName = currentBlock.getFieldValue('AGENT_NAME')
        if (agentName) {
          agentRefs.push(agentName)
        }
      }
      
      currentBlock = currentBlock.getNextBlock()
    }
    
    return agentRefs
  }
  
  /**
   * 获取工作区的 XML 表示
   * @param workspace Blockly 工作区
   */
  static getWorkspaceXml(workspace: Blockly.WorkspaceSvg): string {
    const xml = Blockly.Xml.workspaceToDom(workspace)
    return Blockly.Xml.domToText(xml)
  }
  
  /**
   * 从 XML 加载工作区
   * @param workspace Blockly 工作区
   * @param xmlText XML 文本
   */
  static loadWorkspaceFromXml(workspace: Blockly.WorkspaceSvg, xmlText: string) {
    try {
      const xml = Blockly.utils.xml.textToDom(xmlText)
      workspace.clear()
      Blockly.Xml.domToWorkspace(xml, workspace)
    } catch (error) {
      console.error('加载工作区失败:', error)
      throw new Error('无效的工作区数据')
    }
  }
  
  /**
   * 验证工作区结构
   * @param workspace Blockly 工作区
   */
  static validateWorkspace(workspace: Blockly.WorkspaceSvg): { valid: boolean; errors: string[] } {
    const errors: string[] = []
    const topBlocks = workspace.getTopBlocks()
    
    // 检查是否有结构块
    const structureBlocks = topBlocks.filter(block => block.type === 'structure')
    if (structureBlocks.length === 0) {
      errors.push('工作区必须包含至少一个结构块')
    } else if (structureBlocks.length > 1) {
      errors.push('工作区只能包含一个结构块')
    }
    
    // 检查结构块是否有主智能体
    if (structureBlocks.length === 1) {
      const structureBlock = structureBlocks[0]
      const mainAgentInput = structureBlock.getInput('MAIN_AGENT')
      if (!mainAgentInput?.connection?.targetBlock()) {
        errors.push('结构块必须包含一个主智能体')
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }
}
