import * as Blockly from 'blockly'

/**
 * 自定义 Blockly 块定义
 * 定义智能体结构编辑器中使用的所有块类型
 */

/**
 * 定义所有自定义块
 */
export function defineCustomBlocks() {
  // 结构块
  Blockly.Blocks['structure'] = {
    init: function() {
      this.appendDummyInput()
        .appendField('🏗️ 结构')
        .appendField(new Blockly.FieldTextInput('新结构'), 'NAME')

      this.appendDummyInput()
        .appendField('描述')
        .appendField(new Blockly.FieldTextInput(''), 'DESCRIPTION')

      this.appendDummyInput()
        .appendField('类型')
        .appendField(new Blockly.FieldDropdown([
          ['协调器', 'coordinator'],
          ['顺序执行', 'sequential'],
          ['并发执行', 'concurrent'],
          ['循环执行', 'loop']
        ]), 'TYPE')

      this.appendDummyInput()
        .appendField('全局指令')
        .appendField(new Blockly.FieldTextInput(''), 'GLOBAL_INSTRUCTION')

      this.appendDummyInput()
        .appendField('温度')
        .appendField(new Blockly.FieldNumber(0.7, 0, 1, 0.1), 'TEMPERATURE')

      this.appendDummyInput()
        .appendField('最大输出令牌')
        .appendField(new Blockly.FieldNumber(204800, 1, 1000000), 'MAX_TOKENS')

      this.appendStatementInput('MAIN_AGENT')
        .setCheck('main_agent')
        .appendField('主智能体')

      this.setColour(230)
      this.setTooltip('智能体结构定义，包含主智能体和配置信息')
      this.setHelpUrl('')
      this.setDeletable(false) // 结构块不能删除
    }
  }
  
  // 主智能体块
  Blockly.Blocks['main_agent'] = {
    init: function() {
      this.appendDummyInput()
        .appendField('🤖 主智能体')
        .appendField(new Blockly.FieldTextInput('main_agent'), 'NAME')

      this.appendDummyInput()
        .appendField('描述')
        .appendField(new Blockly.FieldTextInput(''), 'DESCRIPTION')

      this.appendDummyInput()
        .appendField('类型')
        .appendField(new Blockly.FieldDropdown([
          ['顺序智能体', 'sequential_agent'],
          ['并发智能体', 'concurrent_agent'],
          ['循环智能体', 'loop_agent']
        ]), 'AGENT_TYPE')

      this.appendDummyInput()
        .appendField('模型')
        .appendField(new Blockly.FieldTextInput('qwen-max'), 'MODEL')

      this.appendDummyInput()
        .appendField('指令')
        .appendField(new Blockly.FieldTextInput(''), 'INSTRUCTION')

      this.appendValueInput('MAX_ITERATIONS')
        .setCheck('Number')
        .appendField('最大迭代次数')
        .setVisible(false)

      this.appendStatementInput('SUB_STRUCTURES')
        .setCheck('sub_structure')
        .appendField('子结构')

      this.setPreviousStatement(true, 'main_agent')
      this.setColour(160)
      this.setTooltip('主智能体定义，每个结构必须有且仅有一个主智能体')
      this.setHelpUrl('')
      this.setDeletable(false) // 主智能体块不能删除

      // 监听类型变化，显示/隐藏最大迭代次数字段
      this.setOnChange(function(changeEvent: any) {
        if (changeEvent.type === Blockly.Events.BLOCK_FIELD_INTERMEDIATE_CHANGE ||
            changeEvent.type === Blockly.Events.BLOCK_CHANGE) {
          const agentType = this.getFieldValue('AGENT_TYPE')
          const maxIterationsInput = this.getInput('MAX_ITERATIONS')
          if (maxIterationsInput) {
            maxIterationsInput.setVisible(agentType === 'loop_agent')
          }
        }
      })
    }
  }
  
  // 子结构块
  Blockly.Blocks['sub_structure'] = {
    init: function() {
      this.appendDummyInput()
        .appendField('📦 子结构')
        .appendField(new Blockly.FieldTextInput('子结构名称'), 'NAME')

      this.appendDummyInput()
        .appendField('描述')
        .appendField(new Blockly.FieldTextInput(''), 'DESCRIPTION')

      this.appendDummyInput()
        .appendField('类型')
        .appendField(new Blockly.FieldDropdown([
          ['顺序执行', 'sequential'],
          ['并发执行', 'concurrent'],
          ['循环执行', 'loop']
        ]), 'SUB_TYPE')

      this.appendValueInput('MAX_ITERATIONS')
        .setCheck('Number')
        .appendField('最大迭代次数')
        .setVisible(false)

      this.appendStatementInput('CONTENT')
        .setCheck(['sub_structure', 'agent_ref'])
        .appendField('内容')

      this.setPreviousStatement(true, 'sub_structure')
      this.setNextStatement(true, 'sub_structure')
      this.setColour(290)
      this.setTooltip('子结构定义，可以包含智能体引用或嵌套的子结构')
      this.setHelpUrl('')

      // 监听类型变化，显示/隐藏最大迭代次数字段
      this.setOnChange(function(changeEvent: any) {
        if (changeEvent.type === Blockly.Events.BLOCK_FIELD_INTERMEDIATE_CHANGE ||
            changeEvent.type === Blockly.Events.BLOCK_CHANGE) {
          const subType = this.getFieldValue('SUB_TYPE')
          const maxIterationsInput = this.getInput('MAX_ITERATIONS')
          if (maxIterationsInput) {
            maxIterationsInput.setVisible(subType === 'loop')
          }
        }
      })
    }
  }
  
  // 智能体引用块
  Blockly.Blocks['agent_ref'] = {
    init: function() {
      this.appendDummyInput()
        .appendField('🔗 智能体引用')
        .appendField(new Blockly.FieldTextInput('agentName'), 'AGENT_NAME')
      
      this.setPreviousStatement(true, 'agent_ref')
      this.setNextStatement(true, 'agent_ref')
      this.setColour(120)
      this.setTooltip('智能体引用，指向在 agent_config.json 中定义的智能体')
      this.setHelpUrl('')
    }
  }
  
  // 数字块（用于最大迭代次数）
  Blockly.Blocks['number_input'] = {
    init: function() {
      this.appendDummyInput()
        .appendField(new Blockly.FieldNumber(1, 1, 100), 'VALUE')
      
      this.setOutput(true, 'Number')
      this.setColour(230)
      this.setTooltip('数字输入')
      this.setHelpUrl('')
    }
  }
  
  // 配置块
  Blockly.Blocks['config'] = {
    init: function() {
      this.appendDummyInput()
        .appendField('⚙️ 配置')
      
      this.appendDummyInput()
        .appendField('温度')
        .appendField(new Blockly.FieldNumber(0.7, 0, 1, 0.1), 'TEMPERATURE')
      
      this.appendDummyInput()
        .appendField('最大输出令牌')
        .appendField(new Blockly.FieldNumber(204800, 1, 1000000), 'MAX_TOKENS')
      
      this.setPreviousStatement(true, 'config')
      this.setColour(60)
      this.setTooltip('生成内容配置')
      this.setHelpUrl('')
    }
  }
  
  // 指令块
  Blockly.Blocks['instruction'] = {
    init: function() {
      this.appendDummyInput()
        .appendField('📝 指令')
      
      this.appendDummyInput()
        .appendField(new Blockly.FieldTextInput('输入指令内容...'), 'CONTENT')
      
      this.setPreviousStatement(true, 'instruction')
      this.setColour(200)
      this.setTooltip('智能体指令定义')
      this.setHelpUrl('')
    }
  }
}

/**
 * 获取工具箱XML配置 - 简化版本，支持组件库拖拽
 */
export function getToolboxXml(): string {
  return `
    <xml>
      <block type="structure">
        <field name="NAME">新结构</field>
        <field name="TYPE">coordinator</field>
      </block>

      <block type="main_agent">
        <field name="NAME">main_agent</field>
        <field name="AGENT_TYPE">sequential_agent</field>
        <field name="MODEL">qwen-max</field>
      </block>

      <block type="sub_structure">
        <field name="NAME">子结构</field>
        <field name="SUB_TYPE">sequential</field>
      </block>

      <block type="agent_ref">
        <field name="AGENT_NAME">agentName</field>
      </block>

      <block type="config">
        <field name="TEMPERATURE">0.7</field>
        <field name="MAX_TOKENS">204800</field>
      </block>
    </xml>
  `
}

/**
 * 自定义块的代码生成器（可选）
 */
export function defineCodeGenerators() {
  // 这里可以定义如何将块转换为代码
  // 目前我们主要关注可视化编辑，所以暂时不实现
}

/**
 * 验证块连接规则
 */
export function validateBlockConnections(workspace: Blockly.WorkspaceSvg): string[] {
  const errors: string[] = []
  const topBlocks = workspace.getTopBlocks()
  
  // 检查是否有且仅有一个结构块
  const structureBlocks = topBlocks.filter(block => block.type === 'structure')
  if (structureBlocks.length === 0) {
    errors.push('必须有一个结构块作为根节点')
  } else if (structureBlocks.length > 1) {
    errors.push('只能有一个结构块')
  }
  
  // 检查结构块是否有主智能体
  if (structureBlocks.length === 1) {
    const structureBlock = structureBlocks[0]
    const mainAgentInput = structureBlock.getInput('MAIN_AGENT')
    if (!mainAgentInput?.connection?.targetBlock()) {
      errors.push('结构块必须包含一个主智能体')
    }
  }
  
  // 检查子结构是否有内容
  const allBlocks = workspace.getAllBlocks()
  const subStructureBlocks = allBlocks.filter(block => block.type === 'sub_structure')
  
  for (const subBlock of subStructureBlocks) {
    const contentInput = subBlock.getInput('CONTENT')
    if (!contentInput?.connection?.targetBlock()) {
      const blockName = subBlock.getFieldValue('NAME') || '未命名子结构'
      errors.push(`子结构 "${blockName}" 必须包含内容（智能体引用或嵌套子结构）`)
    }
  }
  
  return errors
}
