from typing import Dict, Any, Optional, List, Callable, Sequence
from google.adk.tools.base_tool import BaseTool
from google.adk.tools.base_toolset import BaseToolset
from google.adk.tools.tool_context import ToolContext
from google.genai import types
import json
from .llm_manager import LLMManager

class ToolDescription:
    """工具描述类"""
    def __init__(self,
                 tool_name: str = "",
                 summary: str = "",
                 parameters: Optional[Dict[str, str]] = None,
                 returns: Optional[List[Dict[str, Any]]] = None):
        """
        初始化工具描述
        :param tool_name: 工具名称
        :param summary: 功能概述
        :param parameters: 参数说明
        :param returns: 返回值说明
        """
        self.tool_name = tool_name
        self.summary = summary
        self.parameters = parameters or {}
        self.returns = returns or []

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "tool": self.tool_name,
            "summary": self.summary,
            "parameters": self.parameters,
            "returns": self.returns
        }

    @classmethod
    def from_dict(cls, data: Optional[Dict[str, Any]]) -> Optional['ToolDescription']:
        """
        从字典创建描述对象
        :param data: 描述数据字典
        :return: 描述对象，如果data为None则返回None
        """
        if not data:
            return None
        return cls(
            tool_name=data.get("tool", ""),
            summary=data.get("summary", ""),
            parameters=data.get("parameters", {}),
            returns=data.get("returns", [])
        )

class BaseLLMTool(BaseTool):
    """
    基础LLM工具类：
    用于执行LLM对话并获取JSON格式的结果
    """
    def __init__(self, 
                 name: str,
                 llm_manager: LLMManager,
                 llm_name: str,
                 instruction: str,
                 json_schema: Dict[str, Any],
                 descriptions: Optional[Dict[str, Any]] = None,
                 temperature: float = 0.3):
        """
        初始化LLM工具
        :param name: 工具名称
        :param llm_manager: LLM管理器实例
        :param llm_name: 要使用的LLM名称
        :param instruction: 系统指令/提示词
        :param json_schema: 期望的JSON返回格式schema
        :param description: 工具描述信息
        :param temperature: 温度参数
        """
        # 获取工具描述
        tool_description = "LLM工具"
        if descriptions and descriptions.get("summary"):
            tool_description = descriptions["summary"]
        
        super().__init__(name=name, description=tool_description)
        # self.name = name  # 已经在父类中设置
        self.llm_manager = llm_manager
        self.llm_name = llm_name
        self.instruction = instruction
        self.json_schema = json_schema
        self.temperature = temperature
        self.description: Optional[ToolDescription] = None
        if descriptions:
            self.description = ToolDescription.from_dict(descriptions).summary

    def _get_declaration(self) -> Optional[types.FunctionDeclaration]:
        """
        获取Google Gemini API的FunctionDeclaration格式
        :return: FunctionDeclaration对象
        """
        try:
            # 将json_schema转换为types.Schema格式
            parameters_schema = self._convert_json_schema_to_gemini_schema(self.json_schema)
            
            # 获取工具描述
            tool_description = self.description if self.description else f"LLM工具: {self.name}"
            
            return types.FunctionDeclaration(
                name=self.name,
                description=tool_description,
                parameters=parameters_schema
            )
        except Exception as e:
            # 如果转换失败，返回None
            return None

    def _convert_json_schema_to_gemini_schema(self, schema: Dict[str, Any]) -> types.Schema:
        """
        将JSON Schema转换为Google Gemini API的Schema格式
        :param schema: JSON Schema字典
        :return: types.Schema对象
        """
        # 处理基本类型映射
        type_mapping = {
            "object": types.Type.OBJECT,
            "string": types.Type.STRING,
            "number": types.Type.NUMBER,
            "integer": types.Type.INTEGER,
            "boolean": types.Type.BOOLEAN,
            "array": types.Type.ARRAY
        }
        
        schema_type = schema.get("type", "object")
        gemini_type = type_mapping.get(schema_type, types.Type.OBJECT)
        
        # 构建基础Schema
        gemini_schema = types.Schema(type=gemini_type)
        
        # 处理描述
        if "description" in schema:
            gemini_schema.description = schema["description"]
        
        # 处理对象类型的属性
        if schema_type == "object" and "properties" in schema:
            properties = {}
            for prop_name, prop_schema in schema["properties"].items():
                properties[prop_name] = self._convert_json_schema_to_gemini_schema(prop_schema)
            gemini_schema.properties = properties
            
            # 处理必需字段
            if "required" in schema:
                gemini_schema.required = schema["required"]
        
        # 处理数组类型的items
        elif schema_type == "array" and "items" in schema:
            gemini_schema.items = self._convert_json_schema_to_gemini_schema(schema["items"])
        
        # 处理枚举值
        if "enum" in schema:
            gemini_schema.enum = schema["enum"]
        
        return gemini_schema

    def _build_prompt(self, **kwargs) -> str:
        """
        构建提示词
        :param kwargs: 提示词参数
        :return: 完整提示词
        """
        prompt = f"{self.instruction}\n\n"
        for key, value in kwargs.items():
            prompt += f"{key}: {value}\n"
        prompt += f"\n请严格按照以下JSON格式返回结果，切记：\n{json.dumps(self.json_schema, ensure_ascii=False, indent=2)}"
        return prompt

    async def run_async(self, *, args: Dict[str, Any], tool_context: ToolContext) -> Any:
        """
        ADK框架要求的异步运行方法
        :param args: LLM填充的参数
        :param tool_context: 工具上下文
        :return: 工具执行结果
        """
        return await self.execute(**args)

    async def execute(self, **kwargs) -> Dict[str, Any]:
        """
        执行LLM调用并获取JSON结果
        :param kwargs: 提示词参数
        :return: JSON格式的结果
        """
        try:
            # 构建消息
            messages = [
                {"role": "user", "content": self._build_prompt(**kwargs)}
            ]
            
            # 调用LLM
            response = self.llm_manager.chat(
                self.llm_name,
                messages=messages,
                temperature=self.temperature,
                stream=True  # 启用流式输出
            )
            
            # 提取并解析JSON结果 - 支持流式和非流式
            if hasattr(response, '__iter__') and not isinstance(response, (dict, str)):
                # 流式响应处理 - 改进版本
                result = ""
                chunk_count = 0
                finish_reason = None
                
                try:
                    for chunk in response:
                        chunk_count += 1
                        
                        if hasattr(chunk, 'choices') and chunk.choices and len(chunk.choices) > 0:
                            choice = chunk.choices[0]
                            
                            # 检查完成原因
                            if hasattr(choice, 'finish_reason') and choice.finish_reason:
                                finish_reason = choice.finish_reason
                                # 如果流已结束，跳出循环
                                if finish_reason in ['stop', 'length', 'content_filter']:
                                    break
                            
                            # 提取内容
                            if hasattr(choice, 'delta') and choice.delta:
                                delta = choice.delta
                                if hasattr(delta, 'content') and delta.content:
                                    result += delta.content
                                elif hasattr(delta, 'get'):
                                    content = delta.get("content", "")
                                    if content:
                                        result += content
                        
                        # 防止无限循环 - 设置最大chunk数量
                        if chunk_count > 10000:  # 合理的上限
                            print(f"警告: 流式响应chunk数量超过限制，强制结束")
                            break
                    
                    # 验证流是否正常结束
                    if finish_reason is None and chunk_count > 0:
                        print(f"警告: 流式响应可能未正常结束，接收到{chunk_count}个chunk")
                    
                except Exception as stream_error:
                    print(f"流式响应处理出错: {str(stream_error)}")
                    # 如果流式处理失败，尝试获取已收集的内容
                    if not result:
                        raise stream_error
                
                # 确保结果不为空
                if not result.strip():
                    raise ValueError("流式响应返回空内容")
                    
            else:
                # 非流式响应处理（兼容性）
                if hasattr(response, 'choices') and response.choices:
                    result = response.choices[0].message.content.strip()
                else:
                    result = str(response).strip()

            # print(f"LLM为工具 {self.name} 生成的参数: {result}")

            # 去除可能的Markdown代码块标记
            if result.startswith('```'):
                result = result.split('\n', 1)[1]
            if result.endswith('```'):
                result = result.rsplit('\n', 1)[0]
            result = result.strip()
            try:
                return json.loads(result)
            except json.JSONDecodeError:
                # 如果解析失败，返回标准错误格式
                return {
                    "error": "LLM返回的结果不是有效的JSON格式",
                    "raw_response": result
                }
                
        except Exception as e:
            return {
                "error": f"执行过程出现错误: {str(e)}",
                "details": {
                    "llm_name": self.llm_name,
                    "error_type": type(e).__name__
                }
            }

class LLMToolset(BaseToolset):
    """
    LLM工具集：
    管理一组LLM工具
    """
    def __init__(self, tools: List[BaseLLMTool]):
        """
        初始化工具集
        :param tools: LLM工具列表
        """
        self.tools = tools

    def get_tools(self, readonly_context: ToolContext) -> Sequence[BaseTool]:
        """
        获取可用的工具列表
        :param readonly_context: 工具上下文
        :return: 工具列表
        """
        return self.tools

    def close(self) -> None:
        """
        关闭工具集，清理资源
        """
        self.tools.clear()