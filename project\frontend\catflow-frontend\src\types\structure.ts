/**
 * 结构配置相关的类型定义
 */

/**
 * 生成内容配置
 */
export interface GenerateContentConfig {
  /** 生成温度，控制输出的随机性 (0.0-1.0) */
  temperature: number
  /** 最大输出token数量 */
  max_output_tokens: number
}

/**
 * 子结构定义
 */
export interface SubStructure {
  /** 子结构名称 */
  name: string
  /** 子结构类型 */
  type: 'sequential' | 'concurrent' | 'loop'
  /** 子结构描述 */
  description: string
  /** 智能体引用列表 */
  agent_refs?: string[]
  /** 最大迭代次数（仅适用于loop类型） */
  max_iterations?: number
  /** 嵌套的子结构 */
  sub_structures?: SubStructure[]
}

/**
 * 主智能体配置
 */
export interface MainAgent {
  /** 主智能体名称 */
  name: string
  /** 主智能体类型 */
  type: 'sequential_agent' | 'concurrent_agent' | 'loop_agent'
  /** 使用的语言模型 */
  model: string
  /** 最大迭代次数（仅适用于loop_agent类型） */
  max_iterations?: number
  /** 主智能体的详细指令 */
  instruction: string
  /** 主智能体的描述信息 */
  description: string
  /** 主智能体包含的子结构列表 */
  sub_structures?: SubStructure[]
}

/**
 * 智能体结构配置
 */
export interface StructureConfig {
  /** 结构名称 */
  name: string
  /** 结构描述 */
  description: string
  /** 结构类型 */
  type: 'coordinator' | 'sequential' | 'concurrent' | 'loop'
  /** 全局指令 */
  global_instruction?: string
  /** 内容生成配置 */
  generate_content_config?: GenerateContentConfig
  /** 主智能体配置 */
  main_agent: MainAgent
}

/**
 * 完整的结构配置文件
 */
export interface StructureConfigFile {
  /** 元数据信息 */
  meta: {
    description: string
    purpose: string
    structure: any
    usage: any
  }
  /** 结构配置对象 */
  structures: Record<string, StructureConfig>
}

/**
 * Blockly 块数据接口
 */
export interface BlockData {
  /** 块ID */
  id: string
  /** 块类型 */
  type: 'structure' | 'main_agent' | 'sub_structure' | 'agent_ref'
  /** 名称 */
  name: string
  /** 描述 */
  description: string
  /** 结构类型（仅适用于structure类型） */
  structureType?: 'coordinator' | 'sequential' | 'concurrent' | 'loop'
  /** 全局指令（仅适用于structure类型） */
  globalInstruction?: string
  /** 智能体类型（仅适用于main_agent类型） */
  agentType?: 'sequential_agent' | 'concurrent_agent' | 'loop_agent'
  /** 模型（仅适用于main_agent类型） */
  model?: string
  /** 指令（仅适用于main_agent类型） */
  instruction?: string
  /** 子结构类型（仅适用于sub_structure类型） */
  subStructureType?: 'sequential' | 'concurrent' | 'loop'
  /** 最大迭代次数 */
  maxIterations?: number
  /** 智能体引用列表 */
  agentRefs?: string[]
  /** 温度参数 */
  temperature?: number
  /** 最大输出令牌数 */
  maxOutputTokens?: number
}

/**
 * 工作区状态
 */
export interface WorkspaceState {
  /** 工作区XML */
  xml: string
  /** 缩放级别 */
  scale: number
  /** 滚动位置 */
  scrollX: number
  scrollY: number
  /** 最后修改时间 */
  lastModified: string
}

/**
 * API 响应接口
 */
export interface ApiResponse<T = any> {
  /** 是否成功 */
  success: boolean
  /** 响应数据 */
  data?: T
  /** 错误信息 */
  message?: string
  /** 错误代码 */
  code?: string
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
  /** 是否有效 */
  valid: boolean
  /** 错误列表 */
  errors: ValidationError[]
  /** 警告列表 */
  warnings: ValidationWarning[]
}

/**
 * 验证错误接口
 */
export interface ValidationError {
  /** 错误类型 */
  type: string
  /** 错误信息 */
  message: string
  /** 错误位置 */
  path: string
  /** 严重程度 */
  severity: 'error' | 'warning' | 'info'
}

/**
 * 验证警告接口
 */
export interface ValidationWarning {
  /** 警告类型 */
  type: string
  /** 警告信息 */
  message: string
  /** 警告位置 */
  path: string
}

/**
 * 智能体配置接口
 */
export interface AgentConfig {
  /** 智能体名称 */
  name: string
  /** 智能体类型 */
  type: string
  /** 智能体描述 */
  description: string
  /** 智能体指令 */
  instruction: string
  /** 使用的模型 */
  model: string
  /** 其他配置参数 */
  [key: string]: any
}

/**
 * 模型配置接口
 */
export interface ModelConfig {
  /** 模型名称 */
  name: string
  /** 模型类型 */
  type: string
  /** 模型描述 */
  description: string
  /** 模型参数 */
  parameters: Record<string, any>
  /** 是否可用 */
  available: boolean
}

/**
 * 工具箱项目接口
 */
export interface ToolboxItem {
  /** 项目ID */
  id: string
  /** 项目名称 */
  name: string
  /** 项目图标 */
  icon: string
  /** 项目类型 */
  type: string
  /** 项目描述 */
  description: string
  /** 是否可拖拽 */
  draggable: boolean
}

/**
 * 工具箱分类接口
 */
export interface ToolboxCategory {
  /** 分类ID */
  id: string
  /** 分类名称 */
  name: string
  /** 分类颜色 */
  color: string
  /** 分类项目 */
  items: ToolboxItem[]
  /** 是否展开 */
  expanded: boolean
}

/**
 * 编辑器配置接口
 */
export interface EditorConfig {
  /** 是否显示网格 */
  showGrid: boolean
  /** 网格间距 */
  gridSpacing: number
  /** 是否启用缩放 */
  enableZoom: boolean
  /** 最小缩放比例 */
  minScale: number
  /** 最大缩放比例 */
  maxScale: number
  /** 是否显示垃圾桶 */
  showTrashcan: boolean
  /** 是否启用滚动条 */
  enableScrollbars: boolean
  /** 工具箱位置 */
  toolboxPosition: 'start' | 'end' | 'top' | 'bottom'
}

/**
 * 导出选项接口
 */
export interface ExportOptions {
  /** 导出格式 */
  format: 'json' | 'xml' | 'yaml'
  /** 是否包含元数据 */
  includeMeta: boolean
  /** 是否压缩输出 */
  minify: boolean
  /** 文件名 */
  filename?: string
}

/**
 * 导入选项接口
 */
export interface ImportOptions {
  /** 是否覆盖现有配置 */
  overwrite: boolean
  /** 是否验证配置 */
  validate: boolean
  /** 是否备份现有配置 */
  backup: boolean
}
