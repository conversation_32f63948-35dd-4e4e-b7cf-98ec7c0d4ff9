<script setup lang="ts">
import StructureEditor from './components/StructureEditor.vue'

/**
 * 主应用组件
 * CatFlow 智能体结构编辑器
 */
</script>

<template>
  <div id="app">
    <StructureEditor />
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#app {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

body {
  margin: 0;
  padding: 0;
  height: 100vh;
  width: 100vw;
}

html {
  height: 100%;
  width: 100%;
}
</style>
