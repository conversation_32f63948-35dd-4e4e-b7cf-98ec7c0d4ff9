import json
import os
from typing import Dict, Any, Optional
from litellm import completion
from google.adk.models.lite_llm import LiteLlm

class LLMManager:
    """
    LLM管理核心类：
    - 支持从json配置文件加载多个LLM（如阿里千问plus、OpenAI等）
    - 可通过LLM名字选择模型进行对话
    - 支持配置api地址、apikey、温度等参数
    """
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化LLM管理器，加载配置文件
        :param config_path: 配置文件路径，如果为None则使用默认路径
        """
        if config_path is None:
            config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'llm_config.json')
        self.llm_configs = self._load_config(config_path)

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """
        加载json配置文件，并进行格式校验
        :param config_path: 配置文件路径
        :return: LLM配置字典
        """
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
        except FileNotFoundError:
            raise ValueError(f"找不到配置文件：{config_path}")
        except json.JSONDecodeError:
            raise ValueError(f"配置文件格式错误：不是有效的JSON格式")

        # 校验llms字段
        if 'llms' not in config or not isinstance(config['llms'], dict):
            raise ValueError("配置文件格式错误：缺少'llms'字段或类型不为dict")
        
        for name, llm_conf in config['llms'].items():
            if not isinstance(llm_conf, dict):
                raise ValueError(f"配置文件格式错误：llm '{name}' 配置应为dict类型")
            if 'model' not in llm_conf:
                raise ValueError(f"配置文件格式错误：llm '{name}' 缺少'model'字段")
        
        print(f"成功加载LLM配置文件：{config_path}")
        print(f"可用的LLM模型：{', '.join(config['llms'].keys())}")
        return config.get('llms', {})

    def chat(self, llm_name: str, messages: list, **kwargs) -> Any:
        """
        使用指定LLM进行对话
        :param llm_name: LLM名称（配置文件中定义的名字）
        :param messages: 对话消息列表（[{"role": "user", "content": "你好"}, ...]）
        :param kwargs: 其他可选参数（如temperature等）
        :return: LLM返回结果
        """
        llm_conf = self.llm_configs.get(llm_name)
        if not llm_conf:
            raise ValueError(f"未找到名为{llm_name}的LLM配置")
        
        # 设置API KEY到环境变量（如有）
        if 'api_key_env' in llm_conf and llm_conf.get('api_key'):
            os.environ[llm_conf['api_key_env']] = llm_conf['api_key']
        
        # 构建completion参数
        completion_kwargs = {
            'model': llm_conf['model'],
            'messages': messages,
        }
        
        # 添加配置中的参数
        if 'api_base' in llm_conf:
            completion_kwargs['api_base'] = llm_conf['api_base']
        if 'api_key' in llm_conf:
            completion_kwargs['api_key'] = llm_conf['api_key']
        if 'temperature' in llm_conf:
            completion_kwargs['temperature'] = llm_conf['temperature']
        if 'max_tokens' in llm_conf:
            completion_kwargs['max_tokens'] = llm_conf['max_tokens']
        
        # 添加额外的kwargs参数
        completion_kwargs.update(kwargs)
        
        # 从配置文件中读取成本信息
        if 'input_cost_per_token' in llm_conf:
            completion_kwargs['input_cost_per_token'] = llm_conf['input_cost_per_token']
        if 'output_cost_per_token' in llm_conf:
            completion_kwargs['output_cost_per_token'] = llm_conf['output_cost_per_token']
        
        # 调用litellm completion
        try:
            response = completion(**completion_kwargs)
            return response
        except Exception as e:
            raise ValueError(f"LLM调用失败: {str(e)}")

    def get_model(self, llm_name: str) -> LiteLlm:
        """
        根据LLM名称获取对应的LiteLlm对象
        :param llm_name: LLM名称（配置文件中定义的名字）
        :return: LiteLlm对象
        """
        llm_conf = self.llm_configs.get(llm_name)
        if not llm_conf:
            raise ValueError(f"未找到名为{llm_name}的LLM配置")
        
        # 构建LiteLlm参数
        lite_llm_kwargs = {
            'model': llm_conf['model']
        }
        
        # 添加可选参数
        if 'api_key' in llm_conf:
            lite_llm_kwargs['api_key'] = llm_conf['api_key']
        if 'api_base' in llm_conf:
            lite_llm_kwargs['api_base'] = llm_conf['api_base']
        if 'temperature' in llm_conf:
            lite_llm_kwargs['temperature'] = llm_conf['temperature']
        if 'max_tokens' in llm_conf:
            lite_llm_kwargs['max_tokens'] = llm_conf['max_tokens']
        
        return LiteLlm(**lite_llm_kwargs)

    def list_models(self) -> list[str]:
        """
        获取所有可用的LLM模型名称
        :return: LLM名称列表
        """
        return list(self.llm_configs.keys())
        
    def get_cost_info(self) -> dict:
        """
        获取LLM成本信息
        :return: 成本信息字典
        """
        try:
            import litellm
            
            # 获取litellm的成本信息
            cost_info = {
                'has_completion_cost': hasattr(litellm, 'completion_cost'),
                'supported_models': {}
            }
            
            # 检查每个配置的模型是否支持成本计算
            for model_name, config in self.llm_configs.items():
                model_id = config.get('model', '')
                
                # 检查是否有自定义定价
                has_custom_pricing = 'input_cost_per_token' in config and 'output_cost_per_token' in config
                
                model_info = {
                    'model_id': model_id,
                    'has_custom_pricing': has_custom_pricing
                }
                
                if has_custom_pricing:
                    model_info['input_cost_per_token'] = config['input_cost_per_token']
                    model_info['output_cost_per_token'] = config['output_cost_per_token']
                
                cost_info['supported_models'][model_name] = model_info
            
            return cost_info
        except ImportError:
            return {'error': 'LiteLLM未安装'}
        except Exception as e:
            return {'error': str(e)}