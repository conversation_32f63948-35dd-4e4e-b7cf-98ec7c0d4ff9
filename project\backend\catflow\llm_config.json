{"meta": {"description": "语言模型配置文件", "purpose": "定义系统中可用的语言模型及其连接配置", "structure": {"llms": {"type": "object", "description": "语言模型配置对象，包含所有可用模型的定义", "properties": {"[model_name]": {"type": "object", "description": "单个语言模型的配置", "properties": {"model": {"type": "string", "description": "模型标识符，格式通常为provider/model_name", "required": true, "example": "openai/qwen-max"}, "api_base": {"type": "string", "description": "API基础URL，模型服务的访问地址", "required": true, "example": "https://dashscope.aliyuncs.com/compatible-mode/v1"}, "api_key": {"type": "string", "description": "API密钥，用于身份验证", "required": true, "security": "敏感信息，请妥善保管"}, "api_key_env": {"type": "string", "description": "环境变量名，用于从环境变量读取API密钥", "required": false, "example": "DASHSCOPE_API_KEY"}, "temperature": {"type": "number", "description": "默认生成温度，控制输出随机性", "range": "0.0-1.0", "default": 0.4}, "input_cost_per_token": {"type": "number", "description": "输入token单价，用于成本计算", "unit": "元/token"}, "output_cost_per_token": {"type": "number", "description": "输出token单价，用于成本计算", "unit": "元/token"}}}}}}, "usage": {"description": "使用方法", "steps": ["1. 在llms对象中定义新的语言模型", "2. 设置模型的基本连接信息：model、api_base、api_key", "3. 配置可选的环境变量api_key_env提高安全性", "4. 设置默认temperature和成本信息", "5. 在agent_config.json中引用模型名称"], "security_notes": ["API密钥是敏感信息，建议使用环境变量", "不要将API密钥提交到版本控制系统", "定期轮换API密钥以确保安全"], "examples": {"basic_model": {"model": "openai/gpt-4", "api_base": "https://api.openai.com/v1", "api_key": "your-api-key-here", "api_key_env": "OPENAI_API_KEY", "temperature": 0.7, "input_cost_per_token": 3e-05, "output_cost_per_token": 6e-05}}}}, "llms": {"qwen-plus": {"model": "openai/qwen-plus", "api_base": "https://dashscope.aliyuncs.com/compatible-mode/v1", "api_key": "sk-e1597dbf89db483bbb8c875f28514f4e", "api_key_env": "DASHSCOPE_API_KEY", "temperature": 0.4, "input_cost_per_token": 1.5e-06, "output_cost_per_token": 2e-06}, "qwen-max-latest": {"model": "openai/qwen-max-latest", "api_base": "https://dashscope.aliyuncs.com/compatible-mode/v1", "api_key": "sk-e1597dbf89db483bbb8c875f28514f4e", "api_key_env": "DASHSCOPE_API_KEY", "temperature": 0.4, "input_cost_per_token": 0.0024, "output_cost_per_token": 0.0096}, "qwen-max": {"model": "openai/qwen-max", "api_base": "https://dashscope.aliyuncs.com/compatible-mode/v1", "api_key": "sk-e1597dbf89db483bbb8c875f28514f4e", "api_key_env": "DASHSCOPE_API_KEY", "temperature": 0.4, "input_cost_per_token": 0.0024, "output_cost_per_token": 0.0096}, "qwen2.5-coder-32b-instruct": {"model": "qwen2.5-coder-32b-instruct", "api_base": "https://dashscope.aliyuncs.com/compatible-mode/v1", "api_key": "sk-e1597dbf89db483bbb8c875f28514f4e", "api_key_env": "DASHSCOPE_API_KEY", "temperature": 0.4, "input_cost_per_token": 0.002, "output_cost_per_token": 0.006}}}