import axios from 'axios'
import { mockStructureData, mockAgentData, mockModelData } from '../data/mockData'

/**
 * API 服务类
 * 处理与后端的所有通信
 */

// 开发模式标志
const isDevelopment = import.meta.env.DEV || !import.meta.env.VITE_API_BASE_URL

// 创建 axios 实例
const api = axios.create({
  baseURL: 'http://localhost:8000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    console.log('发送请求:', config.method?.toUpperCase(), config.url)
    return config
  },
  (error) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log('收到响应:', response.status, response.config.url)
    return response
  },
  (error) => {
    console.error('响应错误:', error.response?.status, error.message)
    return Promise.reject(error)
  }
)

/**
 * 结构配置相关的 API 接口
 */
export class StructureAPI {
  /**
   * 获取所有结构配置
   */
  static async getAllStructures() {
    if (isDevelopment) {
      // 开发模式使用模拟数据
      return new Promise(resolve => {
        setTimeout(() => resolve(mockStructureData), 500)
      })
    }

    try {
      const response = await api.get('/structures')
      return response.data
    } catch (error) {
      console.error('获取结构配置失败:', error)
      throw error
    }
  }

  /**
   * 获取指定结构配置
   * @param structureName 结构名称
   */
  static async getStructure(structureName: string) {
    try {
      const response = await api.get(`/structures/${structureName}`)
      return response.data
    } catch (error) {
      console.error('获取结构配置失败:', error)
      throw error
    }
  }

  /**
   * 创建新的结构配置
   * @param structureData 结构数据
   */
  static async createStructure(structureData: any) {
    if (isDevelopment) {
      // 开发模式模拟保存
      return new Promise(resolve => {
        setTimeout(() => {
          console.log('模拟保存结构配置:', structureData)
          resolve({ success: true, message: '结构配置已保存（模拟）' })
        }, 800)
      })
    }

    try {
      const response = await api.post('/structures', structureData)
      return response.data
    } catch (error) {
      console.error('创建结构配置失败:', error)
      throw error
    }
  }

  /**
   * 更新结构配置
   * @param structureName 结构名称
   * @param structureData 结构数据
   */
  static async updateStructure(structureName: string, structureData: any) {
    try {
      const response = await api.put(`/structures/${structureName}`, structureData)
      return response.data
    } catch (error) {
      console.error('更新结构配置失败:', error)
      throw error
    }
  }

  /**
   * 删除结构配置
   * @param structureName 结构名称
   */
  static async deleteStructure(structureName: string) {
    try {
      const response = await api.delete(`/structures/${structureName}`)
      return response.data
    } catch (error) {
      console.error('删除结构配置失败:', error)
      throw error
    }
  }

  /**
   * 验证结构配置
   * @param structureData 结构数据
   */
  static async validateStructure(structureData: any) {
    try {
      const response = await api.post('/structures/validate', structureData)
      return response.data
    } catch (error) {
      console.error('验证结构配置失败:', error)
      throw error
    }
  }

  /**
   * 导出结构配置
   * @param structureName 结构名称
   */
  static async exportStructure(structureName: string) {
    try {
      const response = await api.get(`/structures/${structureName}/export`, {
        responseType: 'blob'
      })
      return response.data
    } catch (error) {
      console.error('导出结构配置失败:', error)
      throw error
    }
  }

  /**
   * 导入结构配置
   * @param file 配置文件
   */
  static async importStructure(file: File) {
    try {
      const formData = new FormData()
      formData.append('file', file)
      
      const response = await api.post('/structures/import', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      return response.data
    } catch (error) {
      console.error('导入结构配置失败:', error)
      throw error
    }
  }
}

/**
 * 智能体配置相关的 API 接口
 */
export class AgentAPI {
  /**
   * 获取所有智能体配置
   */
  static async getAllAgents() {
    if (isDevelopment) {
      // 开发模式使用模拟数据
      return new Promise(resolve => {
        setTimeout(() => resolve(mockAgentData), 300)
      })
    }

    try {
      const response = await api.get('/agents')
      return response.data
    } catch (error) {
      console.error('获取智能体配置失败:', error)
      throw error
    }
  }

  /**
   * 获取指定智能体配置
   * @param agentName 智能体名称
   */
  static async getAgent(agentName: string) {
    try {
      const response = await api.get(`/agents/${agentName}`)
      return response.data
    } catch (error) {
      console.error('获取智能体配置失败:', error)
      throw error
    }
  }
}

/**
 * 模型配置相关的 API 接口
 */
export class ModelAPI {
  /**
   * 获取所有可用模型
   */
  static async getAllModels() {
    if (isDevelopment) {
      // 开发模式使用模拟数据
      return new Promise(resolve => {
        setTimeout(() => resolve(mockModelData), 200)
      })
    }

    try {
      const response = await api.get('/models')
      return response.data
    } catch (error) {
      console.error('获取模型列表失败:', error)
      throw error
    }
  }

  /**
   * 获取指定模型配置
   * @param modelName 模型名称
   */
  static async getModel(modelName: string) {
    try {
      const response = await api.get(`/models/${modelName}`)
      return response.data
    } catch (error) {
      console.error('获取模型配置失败:', error)
      throw error
    }
  }
}

/**
 * 工作区相关的 API 接口
 */
export class WorkspaceAPI {
  /**
   * 保存工作区状态
   * @param workspaceData 工作区数据
   */
  static async saveWorkspace(workspaceData: any) {
    if (isDevelopment) {
      // 开发模式保存到 localStorage
      return new Promise(resolve => {
        setTimeout(() => {
          localStorage.setItem('catflow_workspace', JSON.stringify(workspaceData))
          console.log('工作区状态已保存到 localStorage')
          resolve({ success: true, message: '工作区状态已保存' })
        }, 500)
      })
    }

    try {
      const response = await api.post('/workspace/save', workspaceData)
      return response.data
    } catch (error) {
      console.error('保存工作区失败:', error)
      throw error
    }
  }

  /**
   * 加载工作区状态
   */
  static async loadWorkspace() {
    if (isDevelopment) {
      // 开发模式从 localStorage 加载
      return new Promise(resolve => {
        setTimeout(() => {
          const saved = localStorage.getItem('catflow_workspace')
          if (saved) {
            const workspaceData = JSON.parse(saved)
            console.log('从 localStorage 加载工作区状态')
            resolve(workspaceData)
          } else {
            resolve({ xml: '', scale: 1, scrollX: 0, scrollY: 0 })
          }
        }, 300)
      })
    }

    try {
      const response = await api.get('/workspace/load')
      return response.data
    } catch (error) {
      console.error('加载工作区失败:', error)
      throw error
    }
  }

  /**
   * 清空工作区
   */
  static async clearWorkspace() {
    try {
      const response = await api.delete('/workspace/clear')
      return response.data
    } catch (error) {
      console.error('清空工作区失败:', error)
      throw error
    }
  }
}

// 导出默认的 axios 实例
export default api
