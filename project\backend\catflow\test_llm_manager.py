from base.llm_manager import LLMManager
import asyncio

async def main():
    # 创建LLM管理器（使用默认配置路径）
    manager = LLMManager()
    
    # 打印可用的模型
    print("\n可用的模型列表：")
    for model_name in manager.list_models():
        print(f"- {model_name} -> {manager.get_model(model_name)}")
    
    # 构造对话消息
    messages = [
        {"role": "user", "content": "你好，介绍一下你自己。"}
    ]
    
    # 使用qwen-plus模型进行对话
    try:
        response = manager.chat("qwen-plus", messages)
        print("\nqwen-plus返回：", response)
    except Exception as e:
        print("\n调用失败：", e)

if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main()) 