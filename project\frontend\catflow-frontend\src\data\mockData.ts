/**
 * 模拟数据
 * 用于在没有后端的情况下测试前端功能
 */

import type { StructureConfigFile, AgentConfig, ModelConfig } from '../types/structure'

/**
 * 模拟的结构配置数据
 */
export const mockStructureData: StructureConfigFile = {
  meta: {
    description: "旅行规划智能体结构配置文件",
    purpose: "定义旅行规划系统的多智能体协作结构",
    structure: {},
    usage: {}
  },
  structures: {
    travel_planning_structure: {
      name: "旅行规划结构",
      description: "用于旅行规划的多智能体协作结构",
      type: "coordinator",
      global_instruction: "请以专业、友好的语调回复用户，提供详细且实用的旅行建议。所有回复都应该结构化、清晰，并包含具体的建议和选项。",
      generate_content_config: {
        temperature: 0.7,
        max_output_tokens: 204800
      },
      main_agent: {
        name: "main_agent",
        type: "loop_agent",
        model: "qwen-max",
        max_iterations: 1,
        instruction: "你是一个旅行计划协调器，负责生成完整的旅行计划报告。你需要协调多个专门的子结构来收集信息并生成最终报告。",
        description: "主agent定义，每个结构下必须要有这个agent，且只能有一个",
        sub_structure_refs: ["plan_agent", "sequential_agent", "summary_agent"]
      },
      sub_structures: {
        plan_agent: {
          name: "plan_agent",
          type: "sequential",
          description: "包含旅行计划智能体的顺序结构",
          agent_refs: ["planTravelAgent"]
        },
        sequential_agent: {
          name: "sequential_agent",
          type: "sequential",
          description: "包含天气、餐厅、酒店智能体的顺序结构",
          agent_refs: ["weatherAgent", "restaurantAgent", "hotelAgent"]
        },
        summary_agent: {
          name: "summary_agent",
          type: "sequential",
          description: "生成最终旅行计划报告的智能体结构",
          agent_refs: ["contextAgent", "summaryAgent"]
        }
      }
    },
    market_research_structure: {
      name: "市场调研结构",
      description: "用于全流程市场调研的多智能体协作结构",
      type: "coordinator",
      global_instruction: "以客观、严谨的态度开展市场调研，确保数据真实可靠、分析深入全面。",
      generate_content_config: {
        temperature: 0.4,
        max_output_tokens: 204800
      },
      main_agent: {
        name: "research_coordinator",
        type: "sequential_agent",
        model: "qwen-max",
        instruction: "作为市场调研总协调者，负责统筹全流程子结构协作。",
        description: "市场调研总协调智能体",
        sub_structure_refs: ["data_collection", "data_analysis"]
      },
      sub_structures: {
        data_collection: {
          name: "data_collection",
          type: "concurrent",
          description: "数据采集阶段",
          agent_refs: ["onlineSurveyAgent", "retailAuditAgent"]
        },
        data_analysis: {
          name: "data_analysis",
          type: "sequential",
          description: "数据分析阶段",
          agent_refs: ["dataAnalysisAgent", "reportAgent"]
        }
      }
    },
    simple_structure: {
      name: "简单结构",
      description: "用于测试的简单结构",
      type: "sequential",
      global_instruction: "这是一个简单的测试结构。",
      generate_content_config: {
        temperature: 0.5,
        max_output_tokens: 100000
      },
      main_agent: {
        name: "simple_agent",
        type: "sequential_agent",
        model: "qwen-max",
        instruction: "执行简单的任务。",
        description: "简单的主智能体",
        sub_structure_refs: ["simple_sub"]
      },
      sub_structures: {
        simple_sub: {
          name: "simple_sub",
          type: "sequential",
          description: "简单的子结构",
          agent_refs: ["testAgent"]
        }
      }
    }
  }
}

/**
 * 模拟的智能体配置数据
 */
export const mockAgentData = {
  agents: {
    // 旅行规划相关智能体
    planTravelAgent: {
      name: "旅行计划智能体",
      type: "planning_agent",
      description: "负责制定详细的旅行计划",
      instruction: "根据用户需求制定详细的旅行计划",
      model: "qwen-max"
    },
    weatherAgent: {
      name: "天气查询智能体", 
      type: "weather_agent",
      description: "查询目的地天气信息",
      instruction: "获取指定地点的天气预报信息",
      model: "qwen-turbo"
    },
    restaurantAgent: {
      name: "餐厅推荐智能体",
      type: "restaurant_agent", 
      description: "推荐当地特色餐厅",
      instruction: "推荐目的地的优质餐厅和美食",
      model: "qwen-turbo"
    },
    hotelAgent: {
      name: "酒店推荐智能体",
      type: "hotel_agent",
      description: "推荐合适的住宿",
      instruction: "根据预算和需求推荐合适的酒店",
      model: "qwen-turbo"
    },
    contextAgent: {
      name: "上下文整理智能体",
      type: "context_agent",
      description: "整理和汇总信息",
      instruction: "整理前面收集的所有信息",
      model: "qwen-max"
    },
    summaryAgent: {
      name: "总结报告智能体",
      type: "summary_agent",
      description: "生成最终旅行报告",
      instruction: "生成完整的旅行计划报告",
      model: "qwen-max"
    },

    // 简单测试智能体
    testAgent: {
      name: "测试智能体",
      type: "test_agent",
      description: "用于测试的简单智能体",
      instruction: "执行测试任务",
      model: "qwen-max"
    },
    dataAnalysisAgent: {
      name: "数据分析智能体",
      type: "analysis_agent",
      description: "分析数据",
      instruction: "对数据进行分析",
      model: "qwen-max"
    },
    reportAgent: {
      name: "报告智能体",
      type: "report_agent",
      description: "生成报告",
      instruction: "生成分析报告",
      model: "qwen-max"
    },

    // 市场调研相关智能体
    onlineSurveyAgent: {
      name: "在线调研智能体",
      type: "survey_agent",
      description: "执行在线问卷调研",
      instruction: "设计和执行在线调研问卷",
      model: "qwen-max"
    },
    retailAuditAgent: {
      name: "零售审计智能体",
      type: "audit_agent", 
      description: "进行零售渠道审计",
      instruction: "收集零售终端的销售数据",
      model: "qwen-turbo"
    },
    socialListeningAgent: {
      name: "社交监听智能体",
      type: "social_agent",
      description: "监听社交媒体声音",
      instruction: "分析社交媒体上的相关讨论",
      model: "qwen-turbo"
    },
    industryReportAgent: {
      name: "行业报告智能体",
      type: "report_agent",
      description: "收集行业研究报告",
      instruction: "搜集和分析行业研究报告",
      model: "qwen-max"
    },
    dataCleaningAgent: {
      name: "数据清洗智能体",
      type: "cleaning_agent",
      description: "清洗和预处理数据",
      instruction: "对收集的数据进行清洗和预处理",
      model: "qwen-turbo"
    },
    formatConversionAgent: {
      name: "格式转换智能体",
      type: "conversion_agent",
      description: "统一数据格式",
      instruction: "将不同格式的数据转换为统一格式",
      model: "qwen-turbo"
    },
    normalizationAgent: {
      name: "标准化智能体",
      type: "normalization_agent",
      description: "数据标准化处理",
      instruction: "对数据进行标准化和归一化处理",
      model: "qwen-turbo"
    },
    duplicateRemovalAgent: {
      name: "去重智能体",
      type: "dedup_agent",
      description: "移除重复数据",
      instruction: "识别和移除重复的数据记录",
      model: "qwen-turbo"
    },
    accuracyCheckAgent: {
      name: "准确性检查智能体",
      type: "accuracy_agent",
      description: "检查数据准确性",
      instruction: "验证数据的准确性和可靠性",
      model: "qwen-max"
    },
    sampleVerificationAgent: {
      name: "样本验证智能体",
      type: "verification_agent",
      description: "验证样本代表性",
      instruction: "检查样本的代表性和有效性",
      model: "qwen-max"
    },
    crossSourceAgent: {
      name: "交叉验证智能体",
      type: "cross_agent",
      description: "进行交叉数据验证",
      instruction: "通过多个数据源进行交叉验证",
      model: "qwen-max"
    },
    competitorAnalysisAgent: {
      name: "竞争对手分析智能体",
      type: "competitor_agent",
      description: "分析竞争对手情况",
      instruction: "深入分析主要竞争对手的策略和表现",
      model: "qwen-max"
    },
    consumerInsightAgent: {
      name: "消费者洞察智能体",
      type: "consumer_agent",
      description: "分析消费者行为",
      instruction: "深入分析消费者需求和行为模式",
      model: "qwen-max"
    },
    trendForecastAgent: {
      name: "趋势预测智能体",
      type: "trend_agent",
      description: "预测市场趋势",
      instruction: "基于数据预测未来市场趋势",
      model: "qwen-max"
    },
    pricingStrategyAgent: {
      name: "定价策略智能体",
      type: "pricing_agent",
      description: "制定定价策略",
      instruction: "基于市场分析制定最优定价策略",
      model: "qwen-max"
    },
    insightIntegrationAgent: {
      name: "洞察整合智能体",
      type: "integration_agent",
      description: "整合各维度洞察",
      instruction: "将各个维度的分析结果进行整合",
      model: "qwen-max"
    },
    visualizationAgent: {
      name: "可视化智能体",
      type: "visualization_agent",
      description: "生成数据可视化",
      instruction: "创建清晰的数据可视化图表",
      model: "qwen-turbo"
    },
    recommendationAgent: {
      name: "建议生成智能体",
      type: "recommendation_agent",
      description: "生成商业建议",
      instruction: "基于分析结果生成可执行的商业建议",
      model: "qwen-max"
    },
    reportPolishAgent: {
      name: "报告润色智能体",
      type: "polish_agent",
      description: "优化报告质量",
      instruction: "对最终报告进行润色和优化",
      model: "qwen-max"
    }
  }
}

/**
 * 模拟的模型配置数据
 */
export const mockModelData = {
  models: {
    "qwen-max": {
      name: "通义千问-Max",
      type: "large_language_model",
      description: "阿里云最强大的大语言模型，适合复杂推理任务",
      parameters: {
        max_tokens: 8192,
        temperature_range: [0.0, 2.0],
        supports_streaming: true
      },
      available: true
    },
    "qwen-turbo": {
      name: "通义千问-Turbo", 
      type: "large_language_model",
      description: "高性能大语言模型，平衡了效果和速度",
      parameters: {
        max_tokens: 8192,
        temperature_range: [0.0, 2.0],
        supports_streaming: true
      },
      available: true
    },
    "qwen-plus": {
      name: "通义千问-Plus",
      type: "large_language_model", 
      description: "增强版大语言模型，具有更好的理解能力",
      parameters: {
        max_tokens: 8192,
        temperature_range: [0.0, 2.0],
        supports_streaming: true
      },
      available: true
    },
    "gpt-4": {
      name: "GPT-4",
      type: "large_language_model",
      description: "OpenAI 的最新大语言模型",
      parameters: {
        max_tokens: 8192,
        temperature_range: [0.0, 2.0],
        supports_streaming: true
      },
      available: false
    },
    "claude-3": {
      name: "Claude-3",
      type: "large_language_model",
      description: "Anthropic 的大语言模型",
      parameters: {
        max_tokens: 4096,
        temperature_range: [0.0, 1.0],
        supports_streaming: true
      },
      available: false
    }
  }
}
