{"meta": {"description": "测试结构配置文件", "purpose": "用于测试前端文件导入功能", "structure": {}, "usage": {}}, "structures": {"test_structure": {"name": "测试结构", "description": "用于测试文件导入功能的简单结构", "type": "coordinator", "global_instruction": "这是一个测试结构，用于验证文件导入功能是否正常工作。", "generate_content_config": {"temperature": 0.8, "max_output_tokens": 102400}, "main_agent": {"name": "test_main_agent", "type": "sequential_agent", "model": "qwen-max", "max_iterations": 1, "instruction": "你是一个测试主智能体，负责协调测试流程。", "description": "测试主智能体定义", "sub_structures": [{"name": "test_sub_structure", "type": "sequential", "description": "测试子结构", "agent_refs": ["testAgent1", "testAgent2"]}]}}}, "agents": {"testAgent1": {"name": "测试智能体1", "type": "test_agent", "description": "第一个测试智能体", "instruction": "执行测试任务1", "model": "qwen-turbo"}, "testAgent2": {"name": "测试智能体2", "type": "test_agent", "description": "第二个测试智能体", "instruction": "执行测试任务2", "model": "qwen-turbo"}}, "models": {"qwen-max": {"name": "通义千问Max", "provider": "alibaba", "type": "chat"}, "qwen-turbo": {"name": "通义千问Turbo", "provider": "alibaba", "type": "chat"}}}