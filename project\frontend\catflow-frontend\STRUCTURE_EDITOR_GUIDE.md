# CatFlow 结构编辑器使用指南

## 概述

CatFlow 结构编辑器是一个基于 Vue 3 和 Google Blockly 的可视化智能体结构编辑工具。它允许用户通过拖拽的方式创建和编辑复杂的多智能体协作结构。

## 功能特性

### 🎯 核心功能
- **可视化编辑**: 使用 Blockly 提供直观的拖拽式编辑体验
- **结构嵌套**: 支持无限层级的子结构嵌套
- **属性编辑**: 每个组件的属性都可以单独编辑，每行显示一个属性
- **实时验证**: 实时检查结构的有效性和完整性
- **数据持久化**: 支持保存和加载结构配置

### 🧩 组件类型

#### 1. 结构组件 (🏗️)
- **用途**: 定义整个智能体结构的根节点
- **属性**:
  - 名称: 结构的标识名称
  - 描述: 结构的详细说明
  - 类型: coordinator（协调器）、sequential（顺序）、concurrent（并发）、loop（循环）
  - 全局指令: 适用于整个结构的通用指导
  - 生成配置: 温度和最大输出令牌数

#### 2. 主智能体 (🤖)
- **用途**: 每个结构必须包含的核心智能体
- **属性**:
  - 名称: 智能体名称（通常为 main_agent）
  - 类型: sequential_agent（顺序）、concurrent_agent（并发）、loop_agent（循环）
  - 模型: 使用的语言模型（从可用模型列表选择）
  - 最大迭代次数: 仅适用于 loop_agent 类型
  - 指令: 智能体的详细执行指令
  - 描述: 智能体的功能说明

#### 3. 子结构 (📦)
- **用途**: 可嵌套的结构组件，支持无限层级嵌套
- **属性**:
  - 名称: 子结构的标识名称
  - 描述: 子结构的功能说明
  - 类型: sequential（顺序）、concurrent（并发）、loop（循环）
  - 最大迭代次数: 仅适用于 loop 类型
  - 智能体引用: 从可用智能体列表中选择

#### 4. 智能体引用 (🔗)
- **用途**: 引用在 agent_config.json 中定义的具体智能体
- **属性**:
  - 智能体名称: 从可用智能体列表中选择

## 界面布局

### 工具栏
位于顶部，包含以下功能按钮：
- **🔍+ / 🔍-**: 放大/缩小工作区
- **⚪**: 重置缩放和位置
- **🗑️**: 清空工作区
- **💾**: 保存当前结构
- **📁**: 加载已保存的结构
- **📋**: 加载示例结构

### 工具箱面板
位于左侧，包含可拖拽的组件：
- **结构组件**: 根结构块
- **主智能体**: 主智能体块
- **子结构**: 可嵌套的子结构块

### 工作区
位于中央，用于：
- 拖拽组件进行结构设计
- 连接不同的组件块
- 可视化查看整个结构层次

### 属性面板
位于右侧，用于：
- 编辑选中组件的属性
- 每个属性占用一行，便于查看和编辑
- 智能体引用和模型可从下拉列表选择

## 使用流程

### 1. 创建新结构
1. 从工具箱拖拽"结构"组件到工作区
2. 在属性面板中设置结构名称、类型和描述
3. 配置全局指令和生成参数

### 2. 添加主智能体
1. 从工具箱拖拽"主智能体"组件
2. 将其连接到结构组件的"主智能体"插槽
3. 在属性面板中配置智能体类型、模型和指令

### 3. 添加子结构
1. 从工具箱拖拽"子结构"组件
2. 将其连接到主智能体的"子结构"插槽
3. 配置子结构的类型和属性
4. 可以继续嵌套更多子结构

### 4. 添加智能体引用
1. 在子结构的属性面板中添加智能体引用
2. 从下拉列表中选择可用的智能体
3. 可以添加多个智能体引用

### 5. 保存和验证
1. 点击保存按钮验证结构完整性
2. 系统会自动检查必需的组件和连接
3. 保存成功后可以在后端使用

## 数据结构映射

编辑器生成的可视化结构会自动转换为符合 struct.json 格式的配置：

```json
{
  "structures": {
    "structure_name": {
      "name": "结构名称",
      "description": "结构描述",
      "type": "coordinator",
      "global_instruction": "全局指令",
      "generate_content_config": {
        "temperature": 0.7,
        "max_output_tokens": 204800
      },
      "main_agent": {
        "name": "main_agent",
        "type": "sequential_agent",
        "model": "qwen-max",
        "instruction": "智能体指令",
        "description": "智能体描述",
        "sub_structures": [
          {
            "name": "子结构名称",
            "type": "sequential",
            "description": "子结构描述",
            "agent_refs": ["agent1", "agent2"],
            "sub_structures": [
              // 嵌套子结构...
            ]
          }
        ]
      }
    }
  }
}
```

## 最佳实践

### 结构设计
1. **清晰命名**: 使用描述性的名称和详细的描述
2. **合理分层**: 避免过深的嵌套层级，保持结构清晰
3. **类型选择**: 根据任务特点选择合适的执行类型
4. **循环控制**: 为循环类型设置合适的最大迭代次数

### 智能体配置
1. **模型选择**: 根据任务复杂度选择合适的语言模型
2. **指令编写**: 编写清晰、具体的智能体指令
3. **引用管理**: 确保所有智能体引用都在 agent_config.json 中定义

### 性能优化
1. **并发使用**: 对于独立任务使用并发类型提高效率
2. **顺序控制**: 对于有依赖关系的任务使用顺序类型
3. **参数调优**: 根据需要调整温度和令牌数参数

## 故障排除

### 常见错误
1. **"必须有一个结构块"**: 确保工作区中有且仅有一个结构组件
2. **"结构块必须包含主智能体"**: 确保结构组件连接了主智能体
3. **"子结构必须包含内容"**: 确保每个子结构都有智能体引用或嵌套结构

### 解决方案
1. 检查组件连接是否正确
2. 验证所有必需属性是否已填写
3. 确保智能体引用在后端配置中存在
4. 查看错误提示面板获取详细信息

## 技术架构

- **前端框架**: Vue 3 + TypeScript
- **可视化引擎**: Google Blockly
- **HTTP 客户端**: Axios
- **构建工具**: Vite
- **样式**: CSS3 + Flexbox

## API 集成

编辑器通过以下 API 与后端通信：
- `GET /api/structures`: 获取所有结构配置
- `POST /api/structures`: 创建新的结构配置
- `PUT /api/structures/{name}`: 更新结构配置
- `GET /api/agents`: 获取可用智能体列表
- `GET /api/models`: 获取可用模型列表
- `POST /api/workspace/save`: 保存工作区状态
- `GET /api/workspace/load`: 加载工作区状态
