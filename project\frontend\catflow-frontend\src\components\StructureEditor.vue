<template>
  <div class="structure-editor">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <h1>CatFlow 结构编辑器</h1>
      </div>
      <div class="toolbar-right">
        <button @click="zoomIn" class="toolbar-btn" title="放大">
          <span>🔍+</span>
        </button>
        <button @click="zoomOut" class="toolbar-btn" title="缩小">
          <span>🔍-</span>
        </button>
        <button @click="resetZoom" class="toolbar-btn" title="重置缩放">
          <span>⚪</span>
        </button>
        <button @click="clearWorkspace" class="toolbar-btn" title="清空">
          <span>🗑️</span>
        </button>
        <button @click="saveStructure" class="toolbar-btn save-btn" title="保存">
          <span>💾</span>
        </button>
        <button @click="importFromFile" class="toolbar-btn" title="导入文件">
          <span>📂</span>
        </button>
        <button @click="loadExampleStructure" class="toolbar-btn" title="加载示例">
          <span>📋</span>
        </button>
      </div>
    </div>

    <!-- 隐藏的文件输入元素 -->
    <input 
      ref="fileInput" 
      type="file" 
      accept=".json" 
      style="display: none" 
      @change="handleFileSelect"
    />

    <!-- 错误提示 -->
    <div v-if="errorMessage" class="error-banner">
      <span class="error-icon">⚠️</span>
      <span class="error-text">{{ errorMessage }}</span>
      <button @click="errorMessage = ''" class="error-close">×</button>
    </div>

    <!-- 加载提示 -->
    <div v-if="isLoading" class="loading-banner">
      <span class="loading-icon">⏳</span>
      <span class="loading-text">正在处理...</span>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 工具箱 -->
      <div class="toolbox-panel">
        <div class="toolbox-header">
          <h3>组件工具箱</h3>
        </div>
        <div class="toolbox-content">
          <div class="tool-category">
            <h4>结构组件</h4>
            <div class="tool-item" draggable="true" @dragstart="startDrag($event, 'structure')">
              <span class="tool-icon">🏗️</span>
              <span class="tool-name">结构</span>
            </div>
          </div>
          
          <div class="tool-category">
            <h4>主智能体</h4>
            <div class="tool-item" draggable="true" @dragstart="startDrag($event, 'main_agent')">
              <span class="tool-icon">🤖</span>
              <span class="tool-name">主智能体</span>
            </div>
          </div>
          
          <div class="tool-category">
            <h4>子结构</h4>
            <div class="tool-item" draggable="true" @dragstart="startDrag($event, 'sub_structure')">
              <span class="tool-icon">📦</span>
              <span class="tool-name">子结构</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Blockly 工作区 -->
      <div class="workspace-container">
        <div ref="blocklyDiv" class="blockly-workspace"></div>
      </div>

      <!-- 属性面板 -->
      <div class="properties-panel">
        <div class="properties-header">
          <h3>属性编辑器</h3>
        </div>
        <div class="properties-content">
          <div v-if="selectedBlock" class="property-form">
            <div class="property-group">
              <label class="property-label">名称</label>
              <input 
                v-model="selectedBlock.name" 
                type="text" 
                class="property-input"
                @input="updateBlockProperty('name', $event.target.value)"
              />
            </div>
            
            <div class="property-group">
              <label class="property-label">描述</label>
              <textarea 
                v-model="selectedBlock.description" 
                class="property-textarea"
                @input="updateBlockProperty('description', $event.target.value)"
              ></textarea>
            </div>
            
            <div v-if="selectedBlock.type === 'structure'" class="property-group">
              <label class="property-label">结构类型</label>
              <select 
                v-model="selectedBlock.structureType" 
                class="property-select"
                @change="updateBlockProperty('structureType', $event.target.value)"
              >
                <option value="coordinator">协调器</option>
                <option value="sequential">顺序执行</option>
                <option value="concurrent">并发执行</option>
                <option value="loop">循环执行</option>
              </select>
            </div>
            
            <div v-if="selectedBlock.type === 'structure'" class="property-group">
              <label class="property-label">全局指令</label>
              <textarea 
                v-model="selectedBlock.globalInstruction" 
                class="property-textarea"
                @input="updateBlockProperty('globalInstruction', $event.target.value)"
              ></textarea>
            </div>
            
            <div v-if="selectedBlock.type === 'main_agent'" class="property-group">
              <label class="property-label">智能体类型</label>
              <select 
                v-model="selectedBlock.agentType" 
                class="property-select"
                @change="updateBlockProperty('agentType', $event.target.value)"
              >
                <option value="sequential_agent">顺序智能体</option>
                <option value="concurrent_agent">并发智能体</option>
                <option value="loop_agent">循环智能体</option>
              </select>
            </div>
            
            <div v-if="selectedBlock.type === 'main_agent'" class="property-group">
              <label class="property-label">模型</label>
              <select
                v-model="selectedBlock.model"
                class="property-select"
                @change="updateBlockProperty('model', $event.target.value)"
              >
                <option v-for="model in availableModels" :key="model" :value="model">
                  {{ model }}
                </option>
              </select>
            </div>
            
            <div v-if="selectedBlock.type === 'main_agent' && selectedBlock.agentType === 'loop_agent'" class="property-group">
              <label class="property-label">最大迭代次数</label>
              <input 
                v-model.number="selectedBlock.maxIterations" 
                type="number" 
                class="property-input"
                @input="updateBlockProperty('maxIterations', parseInt($event.target.value))"
              />
            </div>
            
            <div v-if="selectedBlock.type === 'main_agent'" class="property-group">
              <label class="property-label">指令</label>
              <textarea 
                v-model="selectedBlock.instruction" 
                class="property-textarea"
                @input="updateBlockProperty('instruction', $event.target.value)"
              ></textarea>
            </div>
            
            <div v-if="selectedBlock.type === 'sub_structure'" class="property-group">
              <label class="property-label">子结构类型</label>
              <select 
                v-model="selectedBlock.subStructureType" 
                class="property-select"
                @change="updateBlockProperty('subStructureType', $event.target.value)"
              >
                <option value="sequential">顺序执行</option>
                <option value="concurrent">并发执行</option>
                <option value="loop">循环执行</option>
              </select>
            </div>
            
            <div v-if="selectedBlock.type === 'sub_structure' && selectedBlock.subStructureType === 'loop'" class="property-group">
              <label class="property-label">最大迭代次数</label>
              <input 
                v-model.number="selectedBlock.maxIterations" 
                type="number" 
                class="property-input"
                @input="updateBlockProperty('maxIterations', parseInt($event.target.value))"
              />
            </div>
            
            <div v-if="selectedBlock.type === 'sub_structure'" class="property-group">
              <label class="property-label">智能体引用</label>
              <div class="agent-refs-container">
                <div v-for="(ref, index) in selectedBlock.agentRefs" :key="index" class="agent-ref-item">
                  <select
                    v-model="selectedBlock.agentRefs[index]"
                    class="property-select"
                    @change="updateAgentRefs"
                  >
                    <option value="">请选择智能体</option>
                    <option v-for="agent in availableAgents" :key="agent" :value="agent">
                      {{ agent }}
                    </option>
                  </select>
                  <button @click="removeAgentRef(index)" class="remove-btn">×</button>
                </div>
                <button @click="addAgentRef" class="add-btn">+ 添加智能体引用</button>
              </div>
            </div>
            
            <div v-if="selectedBlock.type === 'structure'" class="property-group">
              <label class="property-label">生成配置</label>
              <div class="config-group">
                <label class="config-label">温度</label>
                <input 
                  v-model.number="selectedBlock.temperature" 
                  type="number" 
                  step="0.1" 
                  min="0" 
                  max="1" 
                  class="property-input"
                  @input="updateBlockProperty('temperature', parseFloat($event.target.value))"
                />
              </div>
              <div class="config-group">
                <label class="config-label">最大输出令牌</label>
                <input 
                  v-model.number="selectedBlock.maxOutputTokens" 
                  type="number" 
                  class="property-input"
                  @input="updateBlockProperty('maxOutputTokens', parseInt($event.target.value))"
                />
              </div>
            </div>
          </div>
          
          <div v-else class="no-selection">
            <p>请选择一个组件来编辑属性</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as Blockly from 'blockly'
import { StructureAPI, WorkspaceAPI, AgentAPI, ModelAPI } from '../services/api'
import { BlocklyUtils } from '../utils/blocklyUtils'
import { defineCustomBlocks, getToolboxXml, validateBlockConnections } from '../utils/blockDefinitions'
import type { StructureConfig, BlockData, StructureConfigFile } from '../types/structure'

/**
 * 结构编辑器组件
 * 使用 Blockly 实现可视化的智能体结构编辑
 */

// 响应式数据
const blocklyDiv = ref<HTMLElement>()
const fileInput = ref<HTMLInputElement>()
let workspace: Blockly.WorkspaceSvg | null = null
const selectedBlock = ref<BlockData | null>(null)
const currentStructure = ref<StructureConfig | null>(null)
const availableAgents = ref<string[]>([])
const availableModels = ref<string[]>([])
const isLoading = ref(false)
const errorMessage = ref('')

// 拖拽相关
const startDrag = (event: DragEvent, blockType: string) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('text/plain', blockType)
  }
}

// 工具栏操作
const zoomIn = () => {
  if (workspace) {
    workspace.zoomCenter(1.2)
  }
}

const zoomOut = () => {
  if (workspace) {
    workspace.zoomCenter(0.8)
  }
}

const resetZoom = () => {
  if (workspace) {
    workspace.setScale(1)
    workspace.scrollCenter()
  }
}

const clearWorkspace = () => {
  if (workspace && confirm('确定要清空工作区吗？')) {
    workspace.clear()
    selectedBlock.value = null
  }
}

const saveStructure = async () => {
  if (!workspace) return

  try {
    isLoading.value = true
    errorMessage.value = ''

    // 验证工作区
    const validationErrors = validateBlockConnections(workspace)
    if (validationErrors.length > 0) {
      errorMessage.value = validationErrors.join('\n')
      alert('保存失败：\n' + validationErrors.join('\n'))
      return
    }

    // 转换为结构配置
    const structureConfig = BlocklyUtils.workspaceToStructureConfig(workspace)
    if (!structureConfig) {
      errorMessage.value = '无法生成有效的结构配置'
      alert('保存失败：无法生成有效的结构配置')
      return
    }

    // 保存到后端
    await StructureAPI.createStructure(structureConfig)

    // 保存工作区状态
    const xmlText = BlocklyUtils.getWorkspaceXml(workspace)
    await WorkspaceAPI.saveWorkspace({
      xml: xmlText,
      scale: workspace.scale,
      scrollX: workspace.scrollX,
      scrollY: workspace.scrollY,
      lastModified: new Date().toISOString()
    })

    alert('结构已保存成功')
  } catch (error) {
    console.error('保存结构失败:', error)
    errorMessage.value = '保存失败：' + (error as Error).message
    alert('保存失败：' + (error as Error).message)
  } finally {
    isLoading.value = false
  }
}

const loadStructure = async () => {
  if (!workspace) return

  try {
    isLoading.value = true
    errorMessage.value = ''

    // 加载工作区状态
    const workspaceData = await WorkspaceAPI.loadWorkspace()
    if (workspaceData.xml) {
      BlocklyUtils.loadWorkspaceFromXml(workspace, workspaceData.xml)

      // 恢复缩放和滚动位置
      if (workspaceData.scale) {
        workspace.setScale(workspaceData.scale)
      }
      if (workspaceData.scrollX !== undefined && workspaceData.scrollY !== undefined) {
        workspace.scroll(workspaceData.scrollX, workspaceData.scrollY)
      }
    }

    alert('结构加载成功')
  } catch (error) {
    console.error('加载结构失败:', error)
    errorMessage.value = '加载失败：' + (error as Error).message
    alert('加载失败：' + (error as Error).message)
  } finally {
    isLoading.value = false
  }
}

// 属性更新
const updateBlockProperty = (property: string, value: any) => {
  if (selectedBlock.value) {
    selectedBlock.value[property] = value
    // 这里可以更新 Blockly 块的显示
  }
}

const updateAgentRefs = () => {
  // 更新智能体引用列表
}

const addAgentRef = () => {
  if (selectedBlock.value && selectedBlock.value.agentRefs) {
    selectedBlock.value.agentRefs.push('')
  }
}

const removeAgentRef = (index: number) => {
  if (selectedBlock.value && selectedBlock.value.agentRefs) {
    selectedBlock.value.agentRefs.splice(index, 1)
  }
}

// 初始化数据
const initData = async () => {
  try {
    // 加载可用的智能体列表
    const agentsData = await AgentAPI.getAllAgents()
    availableAgents.value = Object.keys(agentsData.agents || {})

    // 加载可用的模型列表
    const modelsData = await ModelAPI.getAllModels()
    availableModels.value = Object.keys(modelsData.models || {})
  } catch (error) {
    console.error('初始化数据失败:', error)
  }
}

// 生命周期
onMounted(async () => {
  await initData()
  initBlockly()
})

onUnmounted(() => {
  if (workspace) {
    workspace.dispose()
  }
})

/**
 * 初始化 Blockly 工作区
 */
const initBlockly = () => {
  if (!blocklyDiv.value) return

  // 定义自定义块
  defineCustomBlocks()

  // 创建工作区
  workspace = Blockly.inject(blocklyDiv.value, {
    toolbox: getToolboxXml(),
    grid: {
      spacing: 20,
      length: 3,
      colour: '#ccc',
      snap: true
    },
    zoom: {
      controls: true,
      wheel: true,
      startScale: 1.0,
      maxScale: 3,
      minScale: 0.3,
      scaleSpeed: 1.2
    },
    trashcan: true,
    scrollbars: true,
    horizontalLayout: false,
    toolboxPosition: 'start',
    theme: Blockly.Themes.Modern
  })
  
  // 监听块选择事件
  workspace.addChangeListener((event: any) => {
    if (event.type === Blockly.Events.SELECTED) {
      if (event.newElementId) {
        const block = workspace?.getBlockById(event.newElementId)
        if (block) {
          updateSelectedBlock(block)
        }
      } else {
        selectedBlock.value = null
      }
    }
  })
}



/**
 * 更新选中的块信息
 */
const updateSelectedBlock = (block: any) => {
  const blockData: BlockData = {
    id: block.id,
    type: block.type as any,
    name: block.getFieldValue('NAME') || '',
    description: '',
    agentRefs: []
  }

  // 根据块类型设置默认属性
  switch (block.type) {
    case 'structure':
      blockData.structureType = 'coordinator'
      blockData.globalInstruction = ''
      blockData.temperature = 0.7
      blockData.maxOutputTokens = 204800
      break
    case 'main_agent':
      blockData.agentType = 'sequential_agent'
      blockData.model = availableModels.value[0] || 'qwen-max'
      blockData.instruction = ''
      blockData.maxIterations = 1
      break
    case 'sub_structure':
      blockData.subStructureType = 'sequential'
      blockData.maxIterations = 1
      break
  }

  selectedBlock.value = blockData
}

/**
 * 加载示例结构
 */
const loadExampleStructure = async () => {
  if (!workspace) return

  try {
    // 加载旅行规划结构作为示例
    const structuresData = await StructureAPI.getAllStructures()
    const travelStructure = structuresData.structures?.travel_planning_structure

    if (travelStructure) {
      BlocklyUtils.loadStructureToWorkspace(workspace, travelStructure)
      currentStructure.value = travelStructure
    }
  } catch (error) {
    console.error('加载示例结构失败:', error)
  }
}

/**
 * 触发文件选择
 */
const importFromFile = () => {
  if (fileInput.value) {
    fileInput.value.click()
  }
}

/**
 * 处理文件选择
 * @param event 文件选择事件
 */
const handleFileSelect = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (!file) return
  
  // 检查文件类型
  if (!file.name.endsWith('.json')) {
    errorMessage.value = '请选择JSON格式的文件'
    alert('请选择JSON格式的文件')
    return
  }
  
  try {
    isLoading.value = true
    errorMessage.value = ''
    
    // 读取文件内容
    const fileContent = await readFileAsText(file)
    
    // 解析JSON
    let structureData: StructureConfigFile
    try {
      structureData = JSON.parse(fileContent)
    } catch (parseError) {
      throw new Error('JSON文件格式错误，请检查文件内容')
    }
    
    // 验证文件结构
    if (!structureData.structures || typeof structureData.structures !== 'object') {
      throw new Error('文件格式错误：缺少structures配置')
    }
    
    // 获取第一个结构配置
    const structureKeys = Object.keys(structureData.structures)
    if (structureKeys.length === 0) {
      throw new Error('文件中没有找到任何结构配置')
    }
    
    const firstStructureKey = structureKeys[0]
    const structureConfig = structureData.structures[firstStructureKey]
    
    if (!structureConfig || !structureConfig.main_agent) {
      throw new Error('结构配置格式错误：缺少main_agent配置')
    }
    
    // 清空当前工作区
    if (workspace) {
      workspace.clear()
      selectedBlock.value = null
    }
    
    // 加载结构到工作区
    if (workspace) {
      BlocklyUtils.loadStructureToWorkspace(workspace, structureConfig)
      currentStructure.value = structureConfig
    }
    
    alert(`成功导入结构配置：${structureConfig.name || firstStructureKey}`)
    
  } catch (error) {
    console.error('导入文件失败:', error)
    errorMessage.value = '导入失败：' + (error as Error).message
    alert('导入失败：' + (error as Error).message)
  } finally {
    isLoading.value = false
    // 清空文件输入，允许重复选择同一文件
    if (target) {
      target.value = ''
    }
  }
}

/**
 * 读取文件内容为文本
 * @param file 文件对象
 * @returns Promise<string> 文件内容
 */
const readFileAsText = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = (event) => {
      const result = event.target?.result
      if (typeof result === 'string') {
        resolve(result)
      } else {
        reject(new Error('文件读取失败'))
      }
    }
    
    reader.onerror = () => {
      reject(new Error('文件读取失败'))
    }
    
    reader.readAsText(file, 'utf-8')
  })
}
</script>

<style scoped>
.structure-editor {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

/* 工具栏样式 */
.toolbar {
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.toolbar-left h1 {
  color: white;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.toolbar-right {
  display: flex;
  gap: 10px;
}

.toolbar-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.toolbar-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.save-btn {
  background: rgba(76, 175, 80, 0.8);
  border-color: rgba(76, 175, 80, 1);
}

.save-btn:hover {
  background: rgba(76, 175, 80, 1);
}

/* 错误和加载提示 */
.error-banner {
  background: #ffebee;
  border: 1px solid #f44336;
  color: #c62828;
  padding: 12px 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
}

.error-icon {
  font-size: 18px;
}

.error-text {
  flex: 1;
  font-size: 14px;
  white-space: pre-line;
}

.error-close {
  background: none;
  border: none;
  color: #c62828;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-close:hover {
  background: rgba(196, 40, 40, 0.1);
  border-radius: 50%;
}

.loading-banner {
  background: #e3f2fd;
  border: 1px solid #2196f3;
  color: #1976d2;
  padding: 12px 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.loading-icon {
  font-size: 18px;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 14px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  height: calc(100vh - 60px);
}

/* 工具箱面板 */
.toolbox-panel {
  width: 250px;
  background: white;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.toolbox-header {
  padding: 15px;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.toolbox-header h3 {
  margin: 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.toolbox-content {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
}

.tool-category {
  margin-bottom: 20px;
}

.tool-category h4 {
  margin: 0 0 10px 0;
  color: #666;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tool-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  cursor: grab;
  transition: all 0.3s ease;
  margin-bottom: 8px;
}

.tool-item:hover {
  background: #e3f2fd;
  border-color: #2196f3;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);
}

.tool-item:active {
  cursor: grabbing;
}

.tool-icon {
  font-size: 18px;
}

.tool-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

/* 工作区容器 */
.workspace-container {
  flex: 1;
  position: relative;
  background: white;
}

.blockly-workspace {
  width: 100%;
  height: 100%;
}

/* 属性面板 */
.properties-panel {
  width: 350px;
  background: white;
  border-left: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
}

.properties-header {
  padding: 15px;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.properties-header h3 {
  margin: 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.properties-content {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
}

.property-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.property-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.property-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.property-input,
.property-select,
.property-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.property-input:focus,
.property-select:focus,
.property-textarea:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

.property-textarea {
  min-height: 80px;
  resize: vertical;
  font-family: inherit;
}

.config-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-bottom: 10px;
}

.config-label {
  font-size: 13px;
  color: #666;
  font-weight: 500;
}

.agent-refs-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.agent-ref-item {
  display: flex;
  gap: 8px;
  align-items: center;
}

.remove-btn {
  background: #f44336;
  color: white;
  border: none;
  border-radius: 4px;
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  line-height: 1;
}

.remove-btn:hover {
  background: #d32f2f;
}

.add-btn {
  background: #4caf50;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s ease;
}

.add-btn:hover {
  background: #45a049;
}

.no-selection {
  text-align: center;
  color: #666;
  font-style: italic;
  margin-top: 50px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .toolbox-panel {
    width: 200px;
  }

  .properties-panel {
    width: 300px;
  }
}

@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }

  .toolbox-panel,
  .properties-panel {
    width: 100%;
    height: 200px;
  }

  .workspace-container {
    height: calc(100vh - 460px);
  }
}
</style>
