<template>
  <div class="structure-editor">
    <!-- 顶部结构选择区域 -->
    <div class="header-section">
      <div class="structure-selector">
        <label for="structure-select" class="selector-label">选择结构体：</label>
        <select 
          id="structure-select"
          v-model="selectedStructureName" 
          class="structure-select"
          :disabled="isStructureLocked"
          @change="loadSelectedStructure"
        >
          <option value="">请选择结构体</option>
          <option v-for="structName in availableStructures" :key="structName" :value="structName">
            {{ structName }}
          </option>
        </select>
        <span v-if="isStructureLocked" class="lock-indicator">🔒 已锁定</span>
      </div>
      
      <!-- 标准编辑器控制按钮 -->
      <div class="editor-controls">
        <div class="connection-status" :class="{ 'connected': isConnected, 'disconnected': !isConnected }">
          <span class="status-indicator"></span>
          <span class="status-text">{{ isConnected ? '已连接' : '未连接' }}</span>
        </div>
        <button @click="zoomIn" class="control-btn" title="放大">🔍+</button>
        <button @click="zoomOut" class="control-btn" title="缩小">🔍-</button>
        <button @click="resetZoom" class="control-btn" title="重置">⚪</button>
        <button @click="clearWorkspace" class="control-btn" title="清空">🗑️</button>
        <button @click="saveStructure" class="control-btn save-btn" title="保存" :disabled="!isConnected">💾</button>
      </div>
    </div>

    <!-- 错误和加载提示 -->
    <div v-if="errorMessage" class="error-banner">
      <span class="error-icon">⚠️</span>
      <span class="error-text">{{ errorMessage }}</span>
      <button @click="errorMessage = ''" class="error-close">×</button>
    </div>

    <div v-if="isLoading" class="loading-banner">
      <span class="loading-icon">⏳</span>
      <span class="loading-text">正在处理，请稍候...</span>
      <div class="loading-progress"></div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 组件库面板 -->
      <div class="component-library">
        <div class="library-header">
          <h3>组件库</h3>
        </div>
        <div class="library-content">
          <div class="component-item" draggable="true" @dragstart="startDrag($event, 'structure')">
            <span class="component-icon">🏗️</span>
            <span class="component-name">结构</span>
          </div>
          <div class="component-item" draggable="true" @dragstart="startDrag($event, 'main_agent')">
            <span class="component-icon">🤖</span>
            <span class="component-name">主智能体</span>
          </div>
          <div class="component-item" draggable="true" @dragstart="startDrag($event, 'sub_structure')">
            <span class="component-icon">📦</span>
            <span class="component-name">子结构</span>
          </div>
          <div class="component-item" draggable="true" @dragstart="startDrag($event, 'agent_ref')">
            <span class="component-icon">🔗</span>
            <span class="component-name">智能体引用</span>
          </div>
          <div class="component-item" draggable="true" @dragstart="startDrag($event, 'config')">
            <span class="component-icon">⚙️</span>
            <span class="component-name">配置</span>
          </div>
        </div>
      </div>

      <!-- Blockly 工作区 -->
      <div class="workspace-container">
        <div ref="blocklyDiv" class="blockly-workspace"></div>
      </div>

      <!-- 属性面板 -->
      <div class="properties-panel">
        <div class="properties-header">
          <h3>属性编辑器</h3>
        </div>
        <div class="properties-content">
          <div v-if="selectedBlock" class="property-form" @click.stop>
            <!-- 基础属性 -->
            <div class="property-group">
              <label class="property-label">名称</label>
              <input
                v-model="selectedBlock.name"
                type="text"
                class="property-input"
                @input="updateBlockProperty('name', $event.target.value)"
                @click.stop
              />
            </div>
            
            <div class="property-group">
              <label class="property-label">描述</label>
              <textarea
                v-model="selectedBlock.description"
                class="property-textarea"
                @input="updateBlockProperty('description', $event.target.value)"
                @click.stop
              ></textarea>
            </div>
            
            <!-- 结构特有属性 -->
            <template v-if="selectedBlock.type === 'structure'">
              <div class="property-group">
                <label class="property-label">结构类型</label>
                <select
                  v-model="selectedBlock.structureType"
                  class="property-select"
                  @change="updateBlockProperty('structureType', $event.target.value)"
                  @click.stop
                >
                  <option value="coordinator">协调器</option>
                  <option value="sequential">顺序执行</option>
                  <option value="concurrent">并发执行</option>
                  <option value="loop">循环执行</option>
                </select>
              </div>

              <div class="property-group">
                <label class="property-label">全局指令</label>
                <textarea
                  v-model="selectedBlock.globalInstruction"
                  class="property-textarea"
                  @input="updateBlockProperty('globalInstruction', $event.target.value)"
                  @click.stop
                ></textarea>
              </div>

              <div class="property-group">
                <label class="property-label">温度</label>
                <input
                  v-model.number="selectedBlock.temperature"
                  type="number"
                  step="0.1"
                  min="0"
                  max="1"
                  class="property-input"
                  @input="updateBlockProperty('temperature', parseFloat($event.target.value))"
                  @click.stop
                />
              </div>

              <div class="property-group">
                <label class="property-label">最大输出令牌</label>
                <input
                  v-model.number="selectedBlock.maxOutputTokens"
                  type="number"
                  class="property-input"
                  @input="updateBlockProperty('maxOutputTokens', parseInt($event.target.value))"
                  @click.stop
                />
              </div>
            </template>
            
            <!-- 主智能体特有属性 -->
            <template v-if="selectedBlock.type === 'main_agent'">
              <div class="property-group">
                <label class="property-label">智能体类型</label>
                <select
                  v-model="selectedBlock.agentType"
                  class="property-select"
                  @change="updateBlockProperty('agentType', $event.target.value)"
                  @click.stop
                >
                  <option value="sequential_agent">顺序智能体</option>
                  <option value="concurrent_agent">并发智能体</option>
                  <option value="loop_agent">循环智能体</option>
                </select>
              </div>

              <div class="property-group">
                <label class="property-label">模型</label>
                <select
                  v-model="selectedBlock.model"
                  class="property-select"
                  @change="updateBlockProperty('model', $event.target.value)"
                  @click.stop
                >
                  <option v-for="model in availableModels" :key="model" :value="model">
                    {{ model }}
                  </option>
                </select>
              </div>

              <div v-if="selectedBlock.agentType === 'loop_agent'" class="property-group">
                <label class="property-label">最大迭代次数</label>
                <input
                  v-model.number="selectedBlock.maxIterations"
                  type="number"
                  class="property-input"
                  @input="updateBlockProperty('maxIterations', parseInt($event.target.value))"
                  @click.stop
                />
              </div>

              <div class="property-group">
                <label class="property-label">指令</label>
                <textarea
                  v-model="selectedBlock.instruction"
                  class="property-textarea"
                  @input="updateBlockProperty('instruction', $event.target.value)"
                  @click.stop
                ></textarea>
              </div>

              <div class="property-group">
                <label class="property-label">子结构引用</label>
                <div class="sub-structure-refs-container">
                  <div v-for="(ref, index) in selectedBlock.subStructureRefs" :key="index" class="sub-structure-ref-item">
                    <input
                      v-model="selectedBlock.subStructureRefs[index]"
                      type="text"
                      class="property-input"
                      placeholder="子结构名称"
                      @input="updateSubStructureRefs"
                      @click.stop
                    />
                    <button @click.stop="removeSubStructureRef(index)" class="remove-btn">×</button>
                  </div>
                  <button @click.stop="addSubStructureRef" class="add-btn">+ 添加子结构引用</button>
                </div>
              </div>
            </template>
            
            <!-- 子结构特有属性 -->
            <template v-if="selectedBlock.type === 'sub_structure'">
              <div class="property-group">
                <label class="property-label">子结构类型</label>
                <select
                  v-model="selectedBlock.subStructureType"
                  class="property-select"
                  @change="updateBlockProperty('subStructureType', $event.target.value)"
                  @click.stop
                >
                  <option value="sequential">顺序执行</option>
                  <option value="concurrent">并发执行</option>
                  <option value="loop">循环执行</option>
                </select>
              </div>

              <div v-if="selectedBlock.subStructureType === 'loop'" class="property-group">
                <label class="property-label">最大迭代次数</label>
                <input
                  v-model.number="selectedBlock.maxIterations"
                  type="number"
                  class="property-input"
                  @input="updateBlockProperty('maxIterations', parseInt($event.target.value))"
                  @click.stop
                />
              </div>

              <div class="property-group">
                <label class="property-label">智能体引用</label>
                <div class="agent-refs-container">
                  <div v-for="(ref, index) in selectedBlock.agentRefs" :key="index" class="agent-ref-item">
                    <select
                      v-model="selectedBlock.agentRefs[index]"
                      class="property-select"
                      @change="updateAgentRefs"
                      @click.stop
                    >
                      <option value="">请选择智能体</option>
                      <option v-for="agent in availableAgents" :key="agent" :value="agent">
                        {{ agent }}
                      </option>
                    </select>
                    <button @click.stop="removeAgentRef(index)" class="remove-btn">×</button>
                  </div>
                  <button @click.stop="addAgentRef" class="add-btn">+ 添加智能体引用</button>
                </div>
              </div>
            </template>
            
            <!-- 智能体引用特有属性 -->
            <template v-if="selectedBlock.type === 'agent_ref'">
              <div class="property-group">
                <label class="property-label">智能体名称</label>
                <select
                  v-model="selectedBlock.agentName"
                  class="property-select"
                  @change="updateBlockProperty('agentName', $event.target.value)"
                  @click.stop
                >
                  <option value="">请选择智能体</option>
                  <option v-for="agent in availableAgents" :key="agent" :value="agent">
                    {{ agent }}
                  </option>
                </select>
              </div>
            </template>
          </div>
          <div v-else class="no-selection">
            <p>请选择一个组件来编辑其属性</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as Blockly from 'blockly'
import { StructureAPI, AgentAPI, ModelAPI } from '../services/api'
import axios from 'axios'

// 创建API实例用于健康检查
const api = axios.create({
  baseURL: 'http://localhost:8000',
  timeout: 5000
})
import { BlocklyUtils } from '../utils/blocklyUtils'
import { defineCustomBlocks, getToolboxXml, validateBlockConnections } from '../utils/blockDefinitions'
import type { StructureConfig, BlockData } from '../types/structure'

/**
 * 结构编辑器组件
 * 使用 Blockly 实现可视化的智能体结构编辑
 */

// 响应式数据
const blocklyDiv = ref<HTMLElement>()
let workspace: Blockly.WorkspaceSvg | null = null
const selectedBlock = ref<any>(null)
const selectedStructureName = ref('')
const isStructureLocked = ref(false)
const availableStructures = ref<string[]>([])
const availableAgents = ref<string[]>([])
const availableModels = ref<string[]>([])
const isLoading = ref(false)
const errorMessage = ref('')
const isConnected = ref(true)

/**
 * 初始化数据 - 加载可用的结构、智能体和模型列表
 */
const initData = async () => {
  try {
    isLoading.value = true
    errorMessage.value = ''

    console.log('开始初始化数据...')

    // 加载可用的结构列表
    try {
      const structuresData = await StructureAPI.getAllStructures()
      if (structuresData && structuresData.structures) {
        availableStructures.value = Object.keys(structuresData.structures)
        console.log(`成功加载 ${availableStructures.value.length} 个结构`)
      } else {
        console.warn('结构数据为空或格式不正确')
      }
    } catch (structError) {
      console.error('加载结构列表失败:', structError)
      errorMessage.value = '加载结构列表失败，部分功能可能不可用'
    }

    // 加载可用的智能体列表
    try {
      const agentsData = await AgentAPI.getAllAgents()
      if (agentsData && agentsData.agents) {
        availableAgents.value = Object.keys(agentsData.agents)
        console.log(`成功加载 ${availableAgents.value.length} 个智能体`)
      } else {
        console.warn('智能体数据为空或格式不正确')
      }
    } catch (agentError) {
      console.error('加载智能体列表失败:', agentError)
      // 不设置错误消息，因为这不是致命错误
    }

    // 加载可用的模型列表
    try {
      const modelsData = await ModelAPI.getAllModels()
      if (modelsData && modelsData.models) {
        availableModels.value = Object.keys(modelsData.models)
        console.log(`成功加载 ${availableModels.value.length} 个模型`)
      } else {
        console.warn('模型数据为空或格式不正确')
      }
    } catch (modelError) {
      console.error('加载模型列表失败:', modelError)
      // 不设置错误消息，因为这不是致命错误
    }

    console.log('数据初始化完成')
  } catch (error) {
    console.error('初始化数据时发生未知错误:', error)
    errorMessage.value = '初始化失败，请检查网络连接并刷新页面重试'
  } finally {
    isLoading.value = false
  }
}

/**
 * 初始化 Blockly 工作区
 */
const initBlockly = () => {
  if (!blocklyDiv.value) return

  // 定义自定义块
  defineCustomBlocks()

  // 创建工作区 - 禁用工具箱，使用自定义组件库
  workspace = Blockly.inject(blocklyDiv.value, {
    grid: {
      spacing: 20,
      length: 3,
      colour: '#ccc',
      snap: true
    },
    zoom: {
      controls: false, // 禁用内置缩放控件，使用自定义控件
      wheel: true,
      startScale: 1.0,
      maxScale: 3,
      minScale: 0.3,
      scaleSpeed: 1.2
    },
    trashcan: false, // 禁用内置垃圾桶，使用自定义清空按钮
    scrollbars: true,
    horizontalLayout: false,
    theme: Blockly.Themes.Modern,
    media: 'https://unpkg.com/blockly/media/' // 确保媒体资源路径正确
  })

  // 监听块选择事件
  workspace.addChangeListener((event: any) => {
    if (event.type === Blockly.Events.SELECTED) {
      if (event.newElementId) {
        const block = workspace?.getBlockById(event.newElementId)
        if (block) {
          updateSelectedBlock(block)
        }
      } else {
        // 只有在真正取消选择时才清空，避免属性更新时误清空
        if (!event.oldElementId || event.oldElementId === event.newElementId) {
          selectedBlock.value = null
        }
      }
    }
  })

  // 设置拖拽事件
  setupWorkspaceDragEvents()
}

/**
 * 更新选中的块信息
 */
const updateSelectedBlock = (block: Blockly.Block) => {
  const blockData: BlockData = {
    id: block.id,
    type: block.type,
    name: block.getFieldValue('NAME') || '',
    description: block.getFieldValue('DESCRIPTION') || '',
  }

  // 根据块类型添加特定属性
  switch (block.type) {
    case 'structure':
      blockData.structureType = block.getFieldValue('TYPE') || 'coordinator'
      blockData.globalInstruction = block.getFieldValue('GLOBAL_INSTRUCTION') || ''
      blockData.temperature = parseFloat(block.getFieldValue('TEMPERATURE')) || 0.7
      blockData.maxOutputTokens = parseInt(block.getFieldValue('MAX_TOKENS')) || 204800
      break
    case 'main_agent':
      blockData.agentType = block.getFieldValue('AGENT_TYPE') || 'sequential_agent'
      blockData.model = block.getFieldValue('MODEL') || ''
      blockData.maxIterations = parseInt(block.getFieldValue('MAX_ITERATIONS')) || 1
      blockData.instruction = block.getFieldValue('INSTRUCTION') || ''
      blockData.subStructureRefs = []
      break
    case 'sub_structure':
      blockData.subStructureType = block.getFieldValue('SUB_TYPE') || 'sequential'
      blockData.maxIterations = parseInt(block.getFieldValue('MAX_ITERATIONS')) || 1
      blockData.agentRefs = []
      break
    case 'agent_ref':
      blockData.agentName = block.getFieldValue('AGENT_NAME') || ''
      break
  }

  selectedBlock.value = blockData
}

/**
 * 拖拽开始事件
 */
const startDrag = (event: DragEvent, blockType: string) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('text/plain', blockType)
    event.dataTransfer.effectAllowed = 'copy'
  }
}

/**
 * 设置工作区拖拽事件
 */
const setupWorkspaceDragEvents = () => {
  if (!blocklyDiv.value || !workspace) return

  const workspaceDiv = blocklyDiv.value

  workspaceDiv.addEventListener('dragover', (event: DragEvent) => {
    event.preventDefault()
    event.dataTransfer!.dropEffect = 'copy'
  })

  workspaceDiv.addEventListener('drop', (event: DragEvent) => {
    event.preventDefault()

    const blockType = event.dataTransfer?.getData('text/plain')
    if (!blockType || !workspace) return

    // 计算拖拽位置
    const rect = workspaceDiv.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top

    // 转换为工作区坐标
    const workspaceCoords = workspace.screenToWorkspaceCoordinates(x, y)

    // 创建新块
    const newBlock = workspace.newBlock(blockType)
    newBlock.initSvg()
    newBlock.render()
    newBlock.moveBy(workspaceCoords.x, workspaceCoords.y)

    // 选中新创建的块
    workspace.setSelectedBlock(newBlock)
  })
}

/**
 * 工作区缩放控制
 */
const zoomIn = () => {
  if (workspace) {
    workspace.zoomCenter(1.2)
  }
}

const zoomOut = () => {
  if (workspace) {
    workspace.zoomCenter(0.8)
  }
}

const resetZoom = () => {
  if (workspace) {
    workspace.setScale(1.0)
    workspace.scrollCenter()
  }
}

/**
 * 清空工作区
 */
const clearWorkspace = () => {
  if (workspace && confirm('确定要清空工作区吗？此操作不可撤销。')) {
    workspace.clear()
    selectedBlock.value = null
  }
}

/**
 * 加载选中的结构
 */
const loadSelectedStructure = async () => {
  if (!selectedStructureName.value) return

  try {
    isLoading.value = true
    errorMessage.value = ''

    // 获取结构配置
    const structureData = await StructureAPI.getStructure(selectedStructureName.value)

    if (structureData && workspace) {
      // 清空工作区
      workspace.clear()

      // 加载结构到工作区
      BlocklyUtils.loadStructureToWorkspace(workspace, structureData)

      // 锁定结构选择
      isStructureLocked.value = true

      console.log(`成功加载结构: ${selectedStructureName.value}`)
    } else {
      throw new Error('获取的结构数据为空或工作区未初始化')
    }
  } catch (error) {
    console.error('加载结构失败:', error)
    errorMessage.value = '加载结构失败，请重试'
  } finally {
    isLoading.value = false
  }
}

/**
 * 保存结构
 */
const saveStructure = async () => {
  if (!workspace || !selectedStructureName.value) {
    errorMessage.value = '请先选择一个结构'
    return
  }

  try {
    isLoading.value = true
    errorMessage.value = ''

    // 验证工作区
    const validationErrors = validateBlockConnections(workspace)
    if (validationErrors.length > 0) {
      errorMessage.value = validationErrors.join('\n')
      alert('保存失败：\n' + validationErrors.join('\n'))
      return
    }

    // 转换为结构配置
    const structureConfig = BlocklyUtils.workspaceToStructureConfig(workspace)
    if (!structureConfig) {
      errorMessage.value = '无法生成有效的结构配置'
      alert('保存失败：无法生成有效的结构配置')
      return
    }

    // 确保结构名称与选中的名称一致
    structureConfig.name = selectedStructureName.value

    // 保存到后端（只更新当前选中的结构体）
    await StructureAPI.updateStructure(selectedStructureName.value, structureConfig)

    console.log(`成功保存结构: ${selectedStructureName.value}`)
    alert('结构保存成功！')
  } catch (error) {
    console.error('保存结构失败:', error)
    errorMessage.value = '保存失败，请重试'
    alert('保存失败：' + (error as Error).message)
  } finally {
    isLoading.value = false
  }
}

/**
 * 更新块属性
 */
const updateBlockProperty = (prop: string, value: any) => {
  if (!selectedBlock.value || !workspace) return

  const block = workspace.getBlockById(selectedBlock.value.id)
  if (!block) return

  // 暂时禁用事件以避免循环触发
  const wasEnabled = Blockly.Events.isEnabled()
  Blockly.Events.disable()

  try {
    // 更新块的字段值
    const fieldName = prop.toUpperCase()
    if (block.getField(fieldName)) {
      block.setFieldValue(value, fieldName)
    }

    // 更新本地数据
    selectedBlock.value[prop] = value
  } catch (error) {
    console.error('更新属性失败:', error)
  } finally {
    // 恢复事件状态
    if (wasEnabled) {
      Blockly.Events.enable()
    }
  }
}

/**
 * 智能体引用相关方法
 */
const updateAgentRefs = () => {
  if (!selectedBlock.value) return
  // 过滤掉空值
  selectedBlock.value.agentRefs = selectedBlock.value.agentRefs.filter((ref: string) => ref.trim() !== '')
}

const addAgentRef = () => {
  if (!selectedBlock.value) return
  if (!selectedBlock.value.agentRefs) {
    selectedBlock.value.agentRefs = []
  }
  selectedBlock.value.agentRefs.push('')
}

const removeAgentRef = (index: number) => {
  if (!selectedBlock.value || !selectedBlock.value.agentRefs) return
  selectedBlock.value.agentRefs.splice(index, 1)
}

/**
 * 子结构引用相关方法
 */
const updateSubStructureRefs = () => {
  if (!selectedBlock.value) return
  // 过滤掉空值
  selectedBlock.value.subStructureRefs = selectedBlock.value.subStructureRefs.filter((ref: string) => ref.trim() !== '')
}

const addSubStructureRef = () => {
  if (!selectedBlock.value) return
  if (!selectedBlock.value.subStructureRefs) {
    selectedBlock.value.subStructureRefs = []
  }
  selectedBlock.value.subStructureRefs.push('')
}

const removeSubStructureRef = (index: number) => {
  if (!selectedBlock.value || !selectedBlock.value.subStructureRefs) return
  selectedBlock.value.subStructureRefs.splice(index, 1)
}

/**
 * 检查后端连接状态
 */
const checkConnection = async () => {
  try {
    // 尝试获取健康状态
    await api.get('/health')
    isConnected.value = true
    return true
  } catch (error) {
    console.error('连接检查失败:', error)
    isConnected.value = false
    return false
  }
}

// 定期检查连接状态
let connectionCheckInterval: number | null = null

const startConnectionCheck = () => {
  // 初始检查
  checkConnection()

  // 每30秒检查一次
  connectionCheckInterval = window.setInterval(async () => {
    await checkConnection()
  }, 30000)
}

const stopConnectionCheck = () => {
  if (connectionCheckInterval !== null) {
    clearInterval(connectionCheckInterval)
    connectionCheckInterval = null
  }
}

// 全局错误处理
const handleGlobalError = (error: Error) => {
  console.error('全局错误:', error)

  if (error.message.includes('Could not establish connection')) {
    errorMessage.value = '连接失败，请检查网络连接或刷新页面重试'
    isConnected.value = false
  } else if (error.message.includes('Network Error')) {
    errorMessage.value = '网络错误，请检查网络连接'
    isConnected.value = false
  } else if (error.message.includes('timeout')) {
    errorMessage.value = '请求超时，请重试'
    isConnected.value = false
  } else {
    errorMessage.value = `发生错误: ${error.message}`
  }
}

// 生命周期
onMounted(async () => {
  try {
    // 添加全局错误监听
    window.addEventListener('error', (event) => {
      handleGlobalError(event.error)
    })

    window.addEventListener('unhandledrejection', (event) => {
      handleGlobalError(new Error(event.reason))
    })

    await initData()
    initBlockly()
    startConnectionCheck()
  } catch (error) {
    handleGlobalError(error as Error)
  }
})

onUnmounted(() => {
  if (workspace) {
    workspace.dispose()
  }

  // 停止连接检查
  stopConnectionCheck()

  // 清理事件监听器
  window.removeEventListener('error', handleGlobalError)
  window.removeEventListener('unhandledrejection', handleGlobalError)
})
</script>

<style scoped>
/* 主容器 - PC全屏设计 */
.structure-editor {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  min-width: 1920px;
  min-height: 768px;
}

/* 顶部区域 */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: #ffffff;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  z-index: 100;
}

.structure-selector {
  display: flex;
  align-items: center;
  gap: 12px;
}

.selector-label {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.structure-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  min-width: 200px;
  background: white;
}

.structure-select:disabled {
  background: #f5f5f5;
  color: #999;
}

.lock-indicator {
  color: #666;
  font-size: 12px;
}

.editor-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  margin-right: 12px;
}

.connection-status.connected {
  background: #e8f5e8;
  color: #2e7d32;
}

.connection-status.disconnected {
  background: #ffebee;
  color: #c62828;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.connected .status-indicator {
  background: #4caf50;
  animation: pulse-green 2s infinite;
}

.disconnected .status-indicator {
  background: #f44336;
  animation: pulse-red 2s infinite;
}

@keyframes pulse-green {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

@keyframes pulse-red {
  0% { opacity: 1; }
  50% { opacity: 0.3; }
  100% { opacity: 1; }
}

.status-text {
  font-weight: 500;
}

.control-btn {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.control-btn:hover {
  background: #f0f0f0;
  border-color: #bbb;
}

.save-btn {
  background: #4CAF50;
  color: white;
  border-color: #4CAF50;
}

.save-btn:hover {
  background: #45a049;
}

/* 错误和加载提示 */
.error-banner, .loading-banner {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  gap: 8px;
}

.error-banner {
  background: #ffebee;
  border-bottom: 1px solid #ffcdd2;
  color: #c62828;
}

.loading-banner {
  background: #e3f2fd;
  border-bottom: 1px solid #bbdefb;
  color: #1565c0;
  position: relative;
  overflow: hidden;
}

.loading-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: #2196f3;
  animation: loading-progress 2s ease-in-out infinite;
}

@keyframes loading-progress {
  0% {
    width: 0%;
    left: 0;
  }
  50% {
    width: 100%;
    left: 0;
  }
  100% {
    width: 0%;
    left: 100%;
  }
}

.error-close {
  margin-left: auto;
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #c62828;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  display: flex;
  height: calc(100vh - 60px);
}

/* 组件库 */
.component-library {
  width: 200px;
  background: white;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
}

.library-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  background: #fafafa;
}

.library-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.library-content {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.component-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  cursor: grab;
  background: white;
  transition: all 0.2s;
}

.component-item:hover {
  background: #f5f5f5;
  border-color: #bbb;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.component-item:active {
  cursor: grabbing;
}

.component-icon {
  font-size: 18px;
}

.component-name {
  font-size: 14px;
  color: #333;
}

/* 工作区容器 */
.workspace-container {
  flex: 1;
  position: relative;
  background: white;
}

.blockly-workspace {
  width: 100%;
  height: 100%;
}

/* 属性面板 */
.properties-panel {
  width: 320px;
  background: white;
  border-left: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
}

.properties-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  background: #fafafa;
}

.properties-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.properties-content {
  flex: 1;
  overflow-y: auto;
}

.property-form {
  padding: 16px;
}

.no-selection {
  padding: 20px;
  text-align: center;
  color: #666;
}

.property-group {
  margin-bottom: 16px;
}

.property-label {
  display: block;
  margin-bottom: 6px;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.property-input, .property-select, .property-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  font-family: inherit;
}

.property-textarea {
  min-height: 80px;
  resize: vertical;
}

.property-input:focus, .property-select:focus, .property-textarea:focus {
  outline: none;
  border-color: #4CAF50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

/* 智能体引用容器 */
.agent-refs-container, .sub-structure-refs-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.agent-ref-item, .sub-structure-ref-item {
  display: flex;
  gap: 8px;
  align-items: center;
}

.agent-ref-item .property-select, .sub-structure-ref-item .property-input {
  flex: 1;
}

.remove-btn {
  padding: 6px 10px;
  background: #f44336;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.remove-btn:hover {
  background: #d32f2f;
}

.add-btn {
  padding: 8px 12px;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 8px;
}

.add-btn:hover {
  background: #45a049;
}

/* 响应式设计 */
@media (max-width: 1920px) {
  .structure-editor {
    min-width: 1200px;
  }

  .component-library {
    width: 180px;
  }

  .properties-panel {
    width: 280px;
  }
}
</style>
