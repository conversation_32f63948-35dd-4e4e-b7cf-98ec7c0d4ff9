# LoopAgent 循环智能体配置说明

## 概述

LoopAgent 是一个循环执行子智能体的特殊智能体类型，支持设置退出条件和最大循环次数。它基于 Google ADK 的 LoopAgent 实现，适用于需要重复执行某些任务直到满足特定条件的场景。

## 退出条件

LoopAgent 有两种退出条件：

### 1. 事件退出条件（Event Escalation）

当子智能体生成带有 `escalate` 标志的事件时，LoopAgent 会立即停止循环并退出。这是一种动态退出机制，允许子智能体在满足特定条件时主动结束循环。

```python
# 在子智能体中触发退出
if some_condition_met:
    event.actions.escalate = True
    yield event
    return
```

### 2. 最大循环次数限制

通过设置 `max_iterations` 参数来限制最大循环次数。当达到最大循环次数时，LoopAgent 会自动退出。

## 配置方式

### 在 struct.json 中配置

#### 主智能体配置

```json
{
  "main_agent": {
    "name": "main_agent",
    "type": "loop_agent",
    "model": "qwen-max",
    "max_iterations": 3,
    "instruction": "循环执行子任务直到完成或达到最大次数",
    "agent_refs": ["sub_agent1", "sub_agent2"]
  }
}
```

#### 子结构配置

```json
{
  "sub_structures": {
    "loop_agent": {
      "name": "loop_agent",
      "type": "loop",
      "max_iterations": 5,
      "description": "循环执行子智能体，最多5次或直到子智能体发出退出信号",
      "agent_refs": ["weather_agent", "hotel_agent"]
    }
  }
}
```

### 配置参数说明

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `type` | string | 是 | - | 设置为 "loop" 或 "loop_agent" |
| `max_iterations` | integer | 否 | null | 最大循环次数，null 表示无限循环 |
| `name` | string | 是 | - | 智能体名称 |
| `description` | string | 否 | "" | 智能体描述 |
| `agent_refs` | array | 是 | [] | 子智能体引用列表 |

## 执行流程

1. **初始化**：LoopAgent 创建时设置最大循环次数
2. **循环开始**：times_looped = 0
3. **执行子智能体**：按顺序执行 agent_refs 中的每个子智能体
4. **检查退出条件**：
   - 如果子智能体事件包含 `escalate` 标志，立即退出
   - 如果达到 `max_iterations`，退出循环
5. **循环计数**：times_looped += 1
6. **重复步骤3-5**，直到满足退出条件

## 使用场景

### 1. 数据收集与验证

```json
{
  "data_collection_loop": {
    "type": "loop",
    "max_iterations": 10,
    "description": "循环收集数据直到获得足够的有效数据",
    "agent_refs": ["data_collector", "data_validator"]
  }
}
```

### 2. 迭代优化

```json
{
  "optimization_loop": {
    "type": "loop",
    "max_iterations": 5,
    "description": "迭代优化方案直到满足要求",
    "agent_refs": ["solution_generator", "solution_evaluator"]
  }
}
```

### 3. 重试机制

```json
{
  "retry_loop": {
    "type": "loop",
    "max_iterations": 3,
    "description": "重试执行任务，最多3次",
    "agent_refs": ["task_executor", "result_checker"]
  }
}
```

## 最佳实践

### 1. 设置合理的最大循环次数

- 避免无限循环：始终设置 `max_iterations`
- 根据任务复杂度调整：简单任务设置较小值，复杂任务可适当增加
- 考虑性能影响：过多循环可能影响响应时间

### 2. 实现智能退出条件

```python
# 在子智能体中实现智能退出
class DataValidatorAgent:
    async def validate_data(self, data):
        if self.is_data_sufficient(data):
            # 数据足够，触发退出
            event.actions.escalate = True
            return "数据收集完成"
        else:
            return "需要更多数据"
```

### 3. 监控循环状态

- 在子智能体中记录循环次数
- 提供循环进度反馈
- 记录退出原因（达到最大次数 vs 主动退出）

## 注意事项

1. **性能考虑**：循环次数过多可能导致响应时间过长
2. **资源管理**：确保子智能体正确释放资源
3. **错误处理**：子智能体异常不会自动触发退出，需要显式处理
4. **状态管理**：循环过程中的状态变化需要妥善管理
5. **调试支持**：建议在开发阶段设置较小的 `max_iterations` 值

## 代码生成示例

当配置了 LoopAgent 后，StructureManager 会生成如下代码：

```python
# 自动生成的智能体结构代码
from google.adk.agents.sequential_agent import SequentialAgent
from google.adk.agents.llm_agent import LlmAgent
from .loop_agent import LoopAgent

# 创建子结构：loop_agent（类型：loop）
loop_agent = LoopAgent(
    name="loop_agent",
    max_iterations=5,
    sub_agents=[weather_agent, hotel_agent]
)

# 创建主智能体（类型：loop_agent）
main_agent = LoopAgent(
    name="main_agent",
    max_iterations=3,
    sub_agents=[plan_agent, loop_agent, summary_agent]
)
```

## 总结

LoopAgent 提供了灵活的循环执行机制，通过合理配置退出条件和最大循环次数，可以实现各种复杂的循环逻辑。正确使用 LoopAgent 可以大大提高智能体系统的自动化程度和任务完成效率。