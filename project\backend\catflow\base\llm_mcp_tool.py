import json
import subprocess
import asyncio
import os
import time
import shutil
from typing import Dict, Any, Optional, List, Union, Sequence
from google.adk.tools.base_tool import BaseTool
from google.adk.tools.base_toolset import BaseToolset
from google.adk.tools.tool_context import ToolContext
from google.adk.tools.mcp_tool import MCPToolset
from google.adk.tools.mcp_tool.mcp_session_manager import (
    StdioConnectionParams,
    SseConnectionParams
)
from google.adk.auth.auth_schemes import AuthScheme
from google.adk.auth.auth_credential import AuthCredential
from mcp import StdioServerParameters  # 从 mcp 包直接导入
from .llm_manager import LLMManager

class LLMWrappedMCPTool(BaseTool):
    """
    LLM包装的单个MCP工具类：
    为单个MCP工具提供LLM驱动的智能调用能力
    """
    def __init__(self, 
                 mcp_tool: BaseTool,
                 llm_manager: LLMManager,
                 llm_name: str,
                 instruction: str,
                 temperature: float = 0.3):
        """
        初始化LLM包装的MCP工具
        :param mcp_tool: 原始MCP工具实例
        :param llm_manager: LLM管理器实例
        :param llm_name: 要使用的LLM名称
        :param instruction: 系统指令/提示词
        :param temperature: 温度参数
        """
        super().__init__(name=mcp_tool.name, description=f"LLM包装的MCP工具: {mcp_tool.name}")
        self.mcp_tool = mcp_tool
        # self.name = f"llm_{mcp_tool.name}"  # 已经在父类中设置
        self.llm_manager = llm_manager
        self.llm_name = llm_name
        self.instruction = instruction
        self.temperature = temperature

    def _get_declaration(self):
        """
        获取工具声明，使用原始MCP工具的声明
        """
        return self.mcp_tool._get_declaration()

    def _build_prompt(self, **kwargs) -> str:
        """
        构建提示词
        :param kwargs: 提示词参数
        :return: 完整提示词
        """
        # 获取MCP工具的描述信息
        tool_declaration = self.mcp_tool._get_declaration()
        tool_info = f"工具名称: {self.mcp_tool.name}\n"
        if tool_declaration and hasattr(tool_declaration, 'description'):
            tool_info += f"工具描述: {tool_declaration.description}\n"
        if tool_declaration and hasattr(tool_declaration, 'parameters'):
            tool_info += f"工具参数: {tool_declaration.parameters}\n"
        
        prompt = f"{self.instruction}\n\n{tool_info}\n"
        for key, value in kwargs.items():
            prompt += f"{key}: {value}\n"
        prompt += "\n请根据上述信息和用户需求，生成调用该工具所需的参数，以JSON格式返回。"
        return prompt

    async def run_async(self, *, args: Dict[str, Any], tool_context: ToolContext) -> Any:
        """
        ADK框架要求的异步运行方法
        :param args: LLM填充的参数
        :param tool_context: 工具上下文
        :return: 工具执行结果
        """
        return await self.execute(tool_context=tool_context, **args)

    async def execute(self, tool_context: Optional[ToolContext] = None, **kwargs) -> Dict[str, Any]:
        """
        执行LLM驱动的MCP工具调用
        :param tool_context: 工具上下文
        :param kwargs: 执行参数
        :return: 执行结果
        """
        try:
            # 构建消息
            messages = [
                {"role": "user", "content": self._build_prompt(**kwargs)}
            ]
            
            # 调用LLM获取工具参数
            response = self.llm_manager.chat(
                self.llm_name,
                messages=messages,
                temperature=self.temperature,
                stream=True  # 启用流式输出
            )
            
            # 解析LLM响应 - 支持流式和非流式
            if hasattr(response, '__iter__') and not isinstance(response, (dict, str)):
                # 流式响应处理 - 改进版本
                result = ""
                chunk_count = 0
                finish_reason = None
                
                try:
                    for chunk in response:
                        chunk_count += 1
                        
                        if hasattr(chunk, 'choices') and chunk.choices and len(chunk.choices) > 0:
                            choice = chunk.choices[0]
                            
                            # 检查完成原因
                            if hasattr(choice, 'finish_reason') and choice.finish_reason:
                                finish_reason = choice.finish_reason
                                # 如果流已结束，跳出循环
                                if finish_reason in ['stop', 'length', 'content_filter']:
                                    break
                            
                            # 提取内容
                            if hasattr(choice, 'delta') and choice.delta:
                                delta = choice.delta
                                if hasattr(delta, 'content') and delta.content:
                                    result += delta.content
                                elif hasattr(delta, 'get'):
                                    content = delta.get("content", "")
                                    if content:
                                        result += content
                        
                        # 防止无限循环 - 设置最大chunk数量
                        if chunk_count > 10000:  # 合理的上限
                            print(f"警告: 流式响应chunk数量超过限制，强制结束")
                            break
                    
                    # 验证流是否正常结束
                    if finish_reason is None and chunk_count > 0:
                        print(f"警告: 流式响应可能未正常结束，接收到{chunk_count}个chunk")
                    
                except Exception as stream_error:
                    print(f"流式响应处理出错: {str(stream_error)}")
                    # 如果流式处理失败，尝试获取已收集的内容
                    if not result:
                        raise stream_error
                
                # 确保结果不为空
                if not result.strip():
                    raise ValueError("流式响应返回空内容")
                    
            else:
                # 非流式响应处理（兼容性）
                if hasattr(response, 'choices') and response.choices:
                    result = response.choices[0].message.content.strip()
                else:
                    result = str(response).strip()            
            # print(f"LLM为工具 {self.mcp_tool.name} 生成的参数: {result}")

            try:
                tool_args = json.loads(result)
            except json.JSONDecodeError:
                # 如果LLM返回的不是JSON，尝试直接使用原始参数
                tool_args = kwargs
            
            # 调用原始MCP工具
            if tool_context:
                result = await self.mcp_tool.run_async(args=tool_args, tool_context=tool_context)
            else:
                # 创建一个临时的工具上下文
                from google.adk.tools.tool_context import ToolContext
                temp_context = ToolContext()
                result = await self.mcp_tool.run_async(args=tool_args, tool_context=temp_context)
            
            return result
                
        except Exception as e:
            return {
                "error": f"执行LLM包装的MCP工具时出现错误: {str(e)}",
                "details": {
                    "mcp_tool_name": self.mcp_tool.name,
                    "llm_name": self.llm_name,
                    "error_type": type(e).__name__
                }
            }

class LLM_MCPTool(BaseTool):
    """
    LLM驱动的MCP工具类：
    结合LLM能力和MCP协议，实现智能工具调用
    """
    def __init__(self, 
                 name: str,
                 llm_manager: LLMManager,
                 llm_name: str,
                 instruction: str,
                 connection_type: str,  # 'stdio' or 'sse'
                 connection_params: Dict[str, Any],
                 temperature: float = 0.3):
        """
        初始化LLM_MCPTool
        :param name: 工具名称
        :param llm_manager: LLM管理器实例
        :param llm_name: 要使用的LLM名称
        :param instruction: 系统指令/提示词
        :param connection_type: 连接类型（stdio或sse）
        :param connection_params: 连接参数
        :param temperature: 温度参数
        """
        super().__init__(name=name, description=f"LLM驱动的MCP工具: {name}")
        # self.name = name  # 已经在父类中设置
        self.llm_manager = llm_manager
        self.llm_name = llm_name
        self.instruction = instruction
        self.temperature = temperature
        self.mcp_toolset = None
        
        # 创建连接参数
        if connection_type == 'stdio':
            # 检查是否是 mcpServers 格式的配置
            if 'mcpServers' in connection_params:
                # 获取第一个服务配置
                server_name, server_config = next(iter(connection_params['mcpServers'].items()))
                self.conn_params = StdioConnectionParams(
                    server_params=StdioServerParameters(
                        command=server_config.get('command', 'npx'),
                        args=server_config.get('args', []),
                        env=server_config.get('env', {})
                    ),
                    timeout=connection_params.get('timeout', 10.0)
                )
            else:
                self.conn_params = StdioConnectionParams(
                    server_params=StdioServerParameters(
                        command=connection_params.get('command', 'npx'),
                        args=connection_params.get('args', [])
                    ),
                    timeout=connection_params.get('timeout', 10.0)
                )
        elif connection_type == 'sse':
            # 检查是否是 mcpServers 格式的配置
            if 'mcpServers' in connection_params:
                # 获取第一个服务配置
                server_name, server_config = next(iter(connection_params['mcpServers'].items()))
                if server_config.get('type') != 'sse':
                    raise ValueError(f"SSE服务配置类型错误: {server_config.get('type')}")
                self.conn_params = SseConnectionParams(
                    url=server_config['url'],
                    headers=server_config.get('headers', {}),
                    timeout=connection_params.get('timeout', 5.0),
                    sse_read_timeout=connection_params.get('sse_read_timeout', 300.0)
                )
            else:
                self.conn_params = SseConnectionParams(
                    url=connection_params['url'],
                    headers=connection_params.get('headers', {}),
                    timeout=connection_params.get('timeout', 5.0),
                    sse_read_timeout=connection_params.get('sse_read_timeout', 300.0)
                )
        else:
            raise ValueError(f"不支持的连接类型: {connection_type}")

    def is_initialized(self) -> bool:
        """
        检查工具是否已初始化
        :return: 如果已初始化返回True，否则返回False
        """
        return self.mcp_toolset is not None

    async def initialize(self) -> None:
        """
        异步初始化方法，需要在创建实例后调用
        """
        if self.mcp_toolset is not None:
            return
            
        # 获取可用工具列表
        available_tool_names = await self._get_available_tools(self.conn_params)
        
        # 创建最终的工具集
        self.mcp_toolset = MCPToolset(
            connection_params=self.conn_params,
            tool_filter=available_tool_names,  # 使用所有可用的工具
        )

    def _check_command_availability(self, conn_params: StdioConnectionParams) -> bool:
        """
        检查命令和MCP服务器包的可用性（支持npx、uvx等）
        :param conn_params: stdio连接参数
        :return: 如果命令和包都可用返回True
        """
        try:
            command = conn_params.server_params.command
            args = conn_params.server_params.args
            
            # 检查命令是否可用
            if not shutil.which(command):
                print(f"命令不可用: {command}")
                return False
            
            # 检查MCP服务器包是否可用（通过尝试获取帮助信息）
            if args and len(args) > 0:
                package_name = args[0]
                try:
                    # 根据命令类型选择不同的检查方式
                    if 'uvx' in command.lower() or command.endswith('uvx') or command.endswith('uvx.exe'):
                        # uvx命令检查：uvx --help <package>
                        result = subprocess.run(
                            [command, '--help', package_name],
                            capture_output=True,
                            text=True,
                            timeout=10
                        )
                    else:
                        # npx命令检查：npx <package> --help
                        result = subprocess.run(
                            [command, package_name, '--help'],
                            capture_output=True,
                            text=True,
                            timeout=10
                        )
                    
                    if result.returncode == 0:
                        print(f"MCP服务器包可用: {package_name}")
                        return True
                    else:
                        print(f"MCP服务器包不可用: {package_name}")
                        return False
                except subprocess.TimeoutExpired:
                    print(f"检查MCP服务器包超时: {package_name}")
                    return False
                except Exception as e:
                    print(f"检查MCP服务器包时出现异常: {str(e)}")
                    return False
            
            return True
            
        except Exception as e:
            print(f"检查命令可用性时出现异常: {str(e)}")
            return False

    async def _get_available_tools(self, conn_params: Union[StdioConnectionParams, SseConnectionParams]) -> List[str]:
        """
        获取MCP服务提供的可用工具列表
        :param conn_params: 连接参数
        :return: 工具名称列表
        """
        # 如果是stdio连接，先检查命令可用性（支持npx、uvx等）
        if isinstance(conn_params, StdioConnectionParams):
            command = conn_params.server_params.command
            if command and (('npx' in command.lower() or command.endswith('npx.cmd')) or 
                           ('uvx' in command.lower() or command.endswith('uvx') or command.endswith('uvx.exe'))):
                if not self._check_command_availability(conn_params):
                    print(f"命令或MCP服务器包不可用，跳过工具 '{self.name}'")
                    return []
        
        # 对于stdio连接，增加重试机制
        max_retries = 3 if isinstance(conn_params, StdioConnectionParams) else 1
        retry_delay = 2.0  # 秒
        
        for attempt in range(max_retries):
            temp_toolset = None
            try:
                # 创建临时工具集以获取可用工具列表
                temp_toolset = MCPToolset(connection_params=conn_params)
                
                # 获取工具列表
                available_tools = await temp_toolset.get_tools(None)
                available_tool_names = [tool.name for tool in available_tools]
                            
                print(f"MCP工具 '{self.name}' 可用的工具列表: {available_tool_names}")
                return available_tool_names

            except Exception as e:
                error_msg = str(e)
                if attempt < max_retries - 1:
                    print(f"MCP连接失败 (尝试 {attempt + 1}/{max_retries}): {error_msg}，{retry_delay}秒后重试...")
                    await asyncio.sleep(retry_delay)
                else:
                    print(f"MCP连接最终失败: {error_msg}")
                    return []
                        
            finally:
                # 确保临时工具集被关闭
                if temp_toolset is not None:
                    try:
                        await temp_toolset.close()
                    except Exception as cleanup_error:
                        print(f"清理临时工具集时出错: {cleanup_error}")
        
        return []

    def _build_prompt(self, **kwargs) -> str:
        """
        构建提示词
        :param kwargs: 提示词参数
        :return: 完整提示词
        """
        prompt = f"{self.instruction}\n\n"
        for key, value in kwargs.items():
            prompt += f"{key}: {value}\n"
        return prompt

    async def run_async(self, *, args: Dict[str, Any], tool_context: ToolContext) -> Any:
        """
        ADK框架要求的异步运行方法
        注意：LLM_MCPTool现在主要作为工具容器，实际执行由包装的工具完成
        :param args: 参数
        :param tool_context: 工具上下文
        :return: 执行结果
        """
        return {
            "message": "LLM_MCPTool现在作为工具容器，请直接调用其包装的具体工具",
            "available_tools": [t.name for t in await self.get_tools(tool_context)]
        }
            
    async def get_tools(self, readonly_context: Optional[ToolContext] = None) -> Sequence[BaseTool]:
        """获取LLM包装的MCP工具列表"""
        # 确保已初始化
        if not self.is_initialized():
            await self.initialize()
        if self.mcp_toolset is None:
            return []  # 如果初始化失败，返回空列表
        
        # 获取原始MCP工具
        original_tools = await self.mcp_toolset.get_tools()
        
        # 为每个MCP工具创建LLM包装
        wrapped_tools = []
        for tool in original_tools:
            wrapped_tool = LLMWrappedMCPTool(
                mcp_tool=tool,
                llm_manager=self.llm_manager,
                llm_name=self.llm_name,
                instruction=self.instruction,
                temperature=self.temperature
            )
            wrapped_tools.append(wrapped_tool)
            
        return wrapped_tools

    async def close(self) -> None:
        """关闭工具集，清理资源"""
        if self.mcp_toolset is not None:
            await self.mcp_toolset.close()
            self.mcp_toolset = None